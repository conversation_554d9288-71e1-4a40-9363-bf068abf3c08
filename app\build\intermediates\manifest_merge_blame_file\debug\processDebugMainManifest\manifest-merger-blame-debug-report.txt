1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wendy.face"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <uses-feature android:name="android.hardware.camera.any" />
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:5-64
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:19-61
12
13    <uses-permission android:name="android.permission.CAMERA" />
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:5-65
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:22-62
14    <uses-permission
14-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:5-8:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:22-78
16        android:maxSdkVersion="28" />
16-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:8:9-35
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:5-80
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:22-77
18    <uses-permission android:name="android.permission.INTERNET" />
18-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:10:5-67
18-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:10:22-64
19
20    <queries>
20-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
21        <intent>
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
22            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
22-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
22-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
23        </intent>
24    </queries>
25
26    <permission
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
27        android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
31    <!-- <uses-sdk android:minSdkVersion="14"/> -->
32    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
33
34    <application
34-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:12:5-45:19
35        android:allowBackup="true"
35-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:13:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:14:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:15:9-54
41        android:icon="@mipmap/ic_launcher"
41-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:16:9-43
42        android:label="@string/app_name"
42-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:17:9-41
43        android:roundIcon="@mipmap/ic_launcher_round"
43-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:18:9-54
44        android:supportsRtl="true"
44-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:19:9-35
45        android:theme="@style/Theme.Face" >
45-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:20:9-42
46        <activity
46-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:22:9-33:20
47            android:name="com.wendy.face.MainActivity"
47-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:23:13-41
48            android:exported="true"
48-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:24:13-36
49            android:label="@string/app_name"
49-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:25:13-45
50            android:screenOrientation="portrait"
50-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:26:13-49
51            android:theme="@style/Theme.Face.FullScreen" >
51-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:13-57
52            <intent-filter>
52-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:28:13-32:29
53                <action android:name="android.intent.action.MAIN" />
53-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:17-69
53-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:31:17-77
55-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:31:27-74
56            </intent-filter>
57        </activity>
58        <activity
58-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:34:9-40:20
59            android:name="com.wendy.face.TestCameraActivity"
59-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:35:13-47
60            android:exported="false"
60-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:36:13-37
61            android:label="Test Camera"
61-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:37:13-40
62            android:screenOrientation="portrait"
62-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:38:13-49
63            android:theme="@style/Theme.Face" >
63-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:39:13-46
64        </activity>
65        <activity
65-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:41:9-44:40
66            android:name="com.wendy.face.ui.screens.SettingsActivity"
66-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:42:13-56
67            android:exported="false"
67-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:43:13-37
68            android:label="Settings" />
68-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:44:13-37
69
70        <uses-library
70-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
71            android:name="androidx.camera.extensions.impl"
71-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
72            android:required="false" />
72-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
73
74        <service
74-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
75            android:name="androidx.camera.core.impl.MetadataHolderService"
75-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
76            android:enabled="false"
76-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
77            android:exported="false" >
77-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
78            <meta-data
78-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
79                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
79-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
80                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
80-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
81        </service>
82
83        <activity
83-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
84            android:name="androidx.compose.ui.tooling.PreviewActivity"
84-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
85            android:exported="true" />
85-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
86        <activity
86-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
87            android:name="androidx.activity.ComponentActivity"
87-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
88            android:exported="true" />
88-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
89
90        <service
90-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:8:9-14:19
91            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
91-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:9:13-91
92            android:directBootAware="true"
92-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:17:13-43
93            android:exported="false" >
93-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:10:13-37
94            <meta-data
94-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:11:13-13:85
95                android:name="com.google.firebase.components:com.google.mlkit.vision.facemesh.internal.FaceMeshRegistrar"
95-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:12:17-122
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:13:17-82
97            <meta-data
97-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76de22ab496890d5bb43b2576e725184\transformed\vision-common-17.2.1\AndroidManifest.xml:12:13-14:85
98                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
98-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76de22ab496890d5bb43b2576e725184\transformed\vision-common-17.2.1\AndroidManifest.xml:13:17-124
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76de22ab496890d5bb43b2576e725184\transformed\vision-common-17.2.1\AndroidManifest.xml:14:17-82
100            <meta-data
100-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:20:13-22:85
101                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
101-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:21:17-120
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:22:17-82
103        </service>
104
105        <provider
105-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:9:9-13:38
106            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
106-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:10:13-78
107            android:authorities="com.wendy.face.mlkitinitprovider"
107-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:11:13-69
108            android:exported="false"
108-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:12:13-37
109            android:initOrder="99" />
109-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:13:13-35
110
111        <activity
111-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
112            android:name="com.google.android.gms.common.api.GoogleApiActivity"
112-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
113            android:exported="false"
113-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
114            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
114-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
115
116        <meta-data
116-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
117            android:name="com.google.android.gms.version"
117-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
118            android:value="@integer/google_play_services_version" />
118-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
119
120        <provider
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
121            android:name="androidx.startup.InitializationProvider"
121-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
122            android:authorities="com.wendy.face.androidx-startup"
122-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
123            android:exported="false" >
123-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
124            <meta-data
124-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.emoji2.text.EmojiCompatInitializer"
125-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
126                android:value="androidx.startup" />
126-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
127            <meta-data
127-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
128                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
128-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
129                android:value="androidx.startup" />
129-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
130            <meta-data
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
132                android:value="androidx.startup" />
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
133        </provider>
134
135        <service
135-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
136            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
136-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
137            android:exported="false" >
137-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
138            <meta-data
138-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
139                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
139-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
140                android:value="cct" />
140-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
141        </service>
142        <service
142-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
143            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
143-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
144            android:exported="false"
144-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
145            android:permission="android.permission.BIND_JOB_SERVICE" >
145-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
146        </service>
147
148        <receiver
148-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
149            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
149-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
150            android:exported="false" />
150-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
151        <receiver
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
152            android:name="androidx.profileinstaller.ProfileInstallReceiver"
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
153            android:directBootAware="false"
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
154            android:enabled="true"
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
155            android:exported="true"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
156            android:permission="android.permission.DUMP" >
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
157            <intent-filter>
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
158                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
159            </intent-filter>
160            <intent-filter>
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
161                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
162            </intent-filter>
163            <intent-filter>
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
164                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
165            </intent-filter>
166            <intent-filter>
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
167                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
168            </intent-filter>
169        </receiver>
170    </application>
171
172</manifest>
