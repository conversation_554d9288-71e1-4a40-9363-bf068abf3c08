  Manifest android  CAMERA android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  SuppressLint android.annotation  Activity android.app  ActivityResultContracts android.app.Activity  Boolean android.app.Activity  Build android.app.Activity  
ContextCompat android.app.Activity  	Exception android.app.Activity  FaceDetectionApp android.app.Activity  	FaceTheme android.app.Activity  Log android.app.Activity  Manifest android.app.Activity  PackageManager android.app.Activity  PermissionUtils android.app.Activity  SimpleCameraPreview android.app.Activity  Suppress android.app.Activity  Uri android.app.Activity  View android.app.Activity  WindowCompat android.app.Activity  WindowInsets android.app.Activity  WindowInsetsController android.app.Activity  
WindowManager android.app.Activity  getPermissionsToRequest android.app.Activity  
isNotEmpty android.app.Activity  let android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  window android.app.Activity  ContentResolver android.content  
ContentValues android.content  Context android.content  delete android.content.ContentResolver  insert android.content.ContentResolver  openInputStream android.content.ContentResolver  openOutputStream android.content.ContentResolver  update android.content.ContentResolver  android android.content.ContentValues  apply android.content.ContentValues  clear android.content.ContentValues  put android.content.ContentValues  ActivityResultContracts android.content.Context  Boolean android.content.Context  Build android.content.Context  
ContextCompat android.content.Context  	Exception android.content.Context  FaceDetectionApp android.content.Context  	FaceTheme android.content.Context  Log android.content.Context  MODE_PRIVATE android.content.Context  Manifest android.content.Context  PackageManager android.content.Context  PermissionUtils android.content.Context  SimpleCameraPreview android.content.Context  Suppress android.content.Context  Uri android.content.Context  View android.content.Context  WindowCompat android.content.Context  WindowInsets android.content.Context  WindowInsetsController android.content.Context  
WindowManager android.content.Context  contentResolver android.content.Context  getPermissionsToRequest android.content.Context  getSharedPreferences android.content.Context  
isNotEmpty android.content.Context  let android.content.Context  
setContent android.content.Context  ActivityResultContracts android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  	Exception android.content.ContextWrapper  FaceDetectionApp android.content.ContextWrapper  	FaceTheme android.content.ContextWrapper  Log android.content.ContextWrapper  Manifest android.content.ContextWrapper  PackageManager android.content.ContextWrapper  PermissionUtils android.content.ContextWrapper  SimpleCameraPreview android.content.ContextWrapper  Suppress android.content.ContextWrapper  Uri android.content.ContextWrapper  View android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  WindowInsets android.content.ContextWrapper  WindowInsetsController android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  getPermissionsToRequest android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  let android.content.ContextWrapper  
setContent android.content.ContextWrapper  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  
Configuration android.content.res  screenHeightDp !android.content.res.Configuration  Bitmap android.graphics  
BitmapFactory android.graphics  Canvas android.graphics  Matrix android.graphics  Paint android.graphics  Rect android.graphics  CompressFormat android.graphics.Bitmap  Config android.graphics.Bitmap  
asImageBitmap android.graphics.Bitmap  compress android.graphics.Bitmap  createBitmap android.graphics.Bitmap  createScaledBitmap android.graphics.Bitmap  
eraseColor android.graphics.Bitmap  height android.graphics.Bitmap  let android.graphics.Bitmap  recycle android.graphics.Bitmap  width android.graphics.Bitmap  JPEG &android.graphics.Bitmap.CompressFormat  	ARGB_8888 android.graphics.Bitmap.Config  Options android.graphics.BitmapFactory  decodeByteArray android.graphics.BitmapFactory  decodeStream android.graphics.BitmapFactory  apply &android.graphics.BitmapFactory.Options  inJustDecodeBounds &android.graphics.BitmapFactory.Options  inSampleSize &android.graphics.BitmapFactory.Options  	outHeight &android.graphics.BitmapFactory.Options  outWidth &android.graphics.BitmapFactory.Options  Color android.graphics.Canvas  android android.graphics.Canvas  apply android.graphics.Canvas  
drawCircle android.graphics.Canvas  drawRect android.graphics.Canvas  drawText android.graphics.Canvas  sp android.graphics.Canvas  toArgb android.graphics.Canvas  toPx android.graphics.Canvas  BLUE android.graphics.Color  RED android.graphics.Color  YELLOW android.graphics.Color  apply android.graphics.Matrix  
postRotate android.graphics.Matrix  	postScale android.graphics.Matrix  Align android.graphics.Paint  Color android.graphics.Paint  Style android.graphics.Paint  android android.graphics.Paint  apply android.graphics.Paint  ascent android.graphics.Paint  color android.graphics.Paint  descent android.graphics.Paint  isAntiAlias android.graphics.Paint  sp android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  	textAlign android.graphics.Paint  textSize android.graphics.Paint  toArgb android.graphics.Paint  toPx android.graphics.Paint  typeface android.graphics.Paint  CENTER android.graphics.Paint.Align  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  bottom android.graphics.Rect  centerX android.graphics.Rect  centerY android.graphics.Rect  height android.graphics.Rect  left android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  width android.graphics.Rect  DEFAULT_BOLD android.graphics.Typeface  
ExifInterface 
android.media  Image 
android.media  ORIENTATION_FLIP_HORIZONTAL android.media.ExifInterface  ORIENTATION_FLIP_VERTICAL android.media.ExifInterface  ORIENTATION_NORMAL android.media.ExifInterface  ORIENTATION_ROTATE_180 android.media.ExifInterface  ORIENTATION_ROTATE_270 android.media.ExifInterface  ORIENTATION_ROTATE_90 android.media.ExifInterface  ORIENTATION_TRANSPOSE android.media.ExifInterface  ORIENTATION_TRANSVERSE android.media.ExifInterface  TAG_ORIENTATION android.media.ExifInterface  getAttributeInt android.media.ExifInterface  Uri android.net  let android.net.Uri  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  R android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  
IS_PENDING (android.provider.MediaStore.Images.Media  
RELATIVE_PATH (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  
IS_PENDING (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  Log android.util  Size android.util  d android.util.Log  e android.util.Log  w android.util.Log  View android.view  WindowInsets android.view  WindowInsetsController android.view  
WindowManager android.view  ActivityResultContracts  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  FaceDetectionApp  android.view.ContextThemeWrapper  	FaceTheme  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  PermissionUtils  android.view.ContextThemeWrapper  SimpleCameraPreview  android.view.ContextThemeWrapper  Suppress  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  WindowInsets  android.view.ContextThemeWrapper  WindowInsetsController  android.view.ContextThemeWrapper  
WindowManager  android.view.ContextThemeWrapper  getPermissionsToRequest  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  SYSTEM_UI_FLAG_FULLSCREEN android.view.View  SYSTEM_UI_FLAG_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_IMMERSIVE_STICKY android.view.View   SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN android.view.View  %SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION android.view.View  SYSTEM_UI_FLAG_LAYOUT_STABLE android.view.View  systemUiVisibility android.view.View  addFlags android.view.Window  	decorView android.view.Window  insetsController android.view.Window  navigationBars android.view.WindowInsets.Type  
statusBars android.view.WindowInsets.Type  %BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE #android.view.WindowInsetsController  hide #android.view.WindowInsetsController  let #android.view.WindowInsetsController  systemBarsBehavior #android.view.WindowInsetsController  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  ComponentActivity androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  FaceDetectionApp #androidx.activity.ComponentActivity  	FaceTheme #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  PermissionUtils #androidx.activity.ComponentActivity  SimpleCameraPreview #androidx.activity.ComponentActivity  Suppress #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  WindowInsets #androidx.activity.ComponentActivity  WindowInsetsController #androidx.activity.ComponentActivity  
WindowManager #androidx.activity.ComponentActivity  getPermissionsToRequest #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  Bitmap androidx.camera.core  Boolean androidx.camera.core  Camera androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  Context androidx.camera.core  
ContextCompat androidx.camera.core  	Exception androidx.camera.core  	Executors androidx.camera.core  FaceDetectorManager androidx.camera.core  FaceMesh androidx.camera.core  
ImageAnalysis androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  
ImageUtils androidx.camera.core  
InputImage androidx.camera.core  Int androidx.camera.core  LifecycleOwner androidx.camera.core  List androidx.camera.core  Locale androidx.camera.core  Log androidx.camera.core  Preview androidx.camera.core  PreviewView androidx.camera.core  ProcessCameraProvider androidx.camera.core  SimpleDateFormat androidx.camera.core  SuppressLint androidx.camera.core  System androidx.camera.core  TAG androidx.camera.core  Unit androidx.camera.core  Uri androidx.camera.core  also androidx.camera.core  android androidx.camera.core  apply androidx.camera.core  context androidx.camera.core  currentIsBackCamera androidx.camera.core  imageProxyToBitmap androidx.camera.core  saveBitmapToGallery androidx.camera.core  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  also "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  <SAM-CONSTRUCTOR> +androidx.camera.core.ImageAnalysis.Analyzer  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  setTargetResolution *androidx.camera.core.ImageAnalysis.Builder  Builder !androidx.camera.core.ImageCapture  CAPTURE_MODE_MAXIMIZE_QUALITY !androidx.camera.core.ImageCapture  OnImageCapturedCallback !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setCaptureMode )androidx.camera.core.ImageCapture.Builder  setTargetResolution )androidx.camera.core.ImageCapture.Builder  Bitmap 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  
ContextCompat 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  	Exception 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  
ImageUtils 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  Locale 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  Log 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  SimpleDateFormat 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  System 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  TAG 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  android 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  apply 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  context 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  currentIsBackCamera 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  imageProxyToBitmap 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  saveBitmapToGallery 9androidx.camera.core.ImageCapture.OnImageCapturedCallback  message *androidx.camera.core.ImageCaptureException  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  planes androidx.camera.core.ImageProxy  buffer *androidx.camera.core.ImageProxy.PlaneProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  PreviewView androidx.camera.view  PreviewView  androidx.camera.view.PreviewView  	ScaleType  androidx.camera.view.PreviewView  apply  androidx.camera.view.PreviewView  	scaleType  androidx.camera.view.PreviewView  surfaceProvider  androidx.camera.view.PreviewView  FILL_CENTER *androidx.camera.view.PreviewView.ScaleType  Canvas androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AnalysisResultCard "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AsyncImage "androidx.compose.foundation.layout  Bitmap "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  BoxWithConstraints "androidx.compose.foundation.layout  BoxWithConstraintsScope "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Build "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  
CameraView "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FaceAnalysisScreen "androidx.compose.foundation.layout  FaceDetectionApp "androidx.compose.foundation.layout  FaceMesh "androidx.compose.foundation.layout  FaceOverlay "androidx.compose.foundation.layout  	FaceTheme "androidx.compose.foundation.layout  Float "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  
ImageUtils "androidx.compose.foundation.layout  
InputImage "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  IntSize "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  LayerControlPanel "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  LocalConfiguration "androidx.compose.foundation.layout  Log "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PalaceAnalysisResult "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  PermissionUtils "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  ScaleAndOffsetResult "androidx.compose.foundation.layout  SettingsScreen "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Suppress "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TopAppBar "androidx.compose.foundation.layout  TwelvePalacesData "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  View "androidx.compose.foundation.layout  WindowCompat "androidx.compose.foundation.layout  WindowInsets "androidx.compose.foundation.layout  WindowInsetsController "androidx.compose.foundation.layout  
WindowManager "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
asImageBitmap "androidx.compose.foundation.layout  	associate "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  calculateScaleAndOffset "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  first "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getBitmapFromUri "androidx.compose.foundation.layout  getPermissionsToRequest "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  minOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  onGloballyPositioned "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
plusAssign "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  radialGradient "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  shadow "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  with "androidx.compose.foundation.layout  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  AlertDialog +androidx.compose.foundation.layout.BoxScope  	Alignment +androidx.compose.foundation.layout.BoxScope  AnalysisResultCard +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  	ArrowBack +androidx.compose.foundation.layout.BoxScope  
AsyncImage +androidx.compose.foundation.layout.BoxScope  Bitmap +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  BoxWithConstraints +androidx.compose.foundation.layout.BoxScope  Brush +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  CameraControls +androidx.compose.foundation.layout.BoxScope  CameraSelector +androidx.compose.foundation.layout.BoxScope  
CameraView +androidx.compose.foundation.layout.BoxScope  Canvas +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  ContentScale +androidx.compose.foundation.layout.BoxScope  
ContextCompat +androidx.compose.foundation.layout.BoxScope  Face3DPointsDisplay +androidx.compose.foundation.layout.BoxScope  FaceAnalysisScreen +androidx.compose.foundation.layout.BoxScope  FaceDetectionGuide +androidx.compose.foundation.layout.BoxScope  FaceOverlay +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  
InputImage +androidx.compose.foundation.layout.BoxScope  IntSize +androidx.compose.foundation.layout.BoxScope  LaunchedEffect +androidx.compose.foundation.layout.BoxScope  LayerControlPanel +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  LocalConfiguration +androidx.compose.foundation.layout.BoxScope  Log +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  OutlinedTextField +androidx.compose.foundation.layout.BoxScope  PasswordVisualTransformation +androidx.compose.foundation.layout.BoxScope  Preview +androidx.compose.foundation.layout.BoxScope  PreviewView +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Settings +androidx.compose.foundation.layout.BoxScope  SettingsScreen +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  TwelvePalacesData +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  also +androidx.compose.foundation.layout.BoxScope  android +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  
asImageBitmap +androidx.compose.foundation.layout.BoxScope  	associate +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  calculateScaleAndOffset +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  drawFacePoints +androidx.compose.foundation.layout.BoxScope  	emptyList +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  first +androidx.compose.foundation.layout.BoxScope  getValue +androidx.compose.foundation.layout.BoxScope  heightIn +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  items +androidx.compose.foundation.layout.BoxScope  launch +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  minOf +androidx.compose.foundation.layout.BoxScope  mutableStateOf +androidx.compose.foundation.layout.BoxScope  onGloballyPositioned +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  
plusAssign +androidx.compose.foundation.layout.BoxScope  provideDelegate +androidx.compose.foundation.layout.BoxScope  remember +androidx.compose.foundation.layout.BoxScope  scale +androidx.compose.foundation.layout.BoxScope  setValue +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  spacedBy +androidx.compose.foundation.layout.BoxScope  to +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  	Alignment :androidx.compose.foundation.layout.BoxWithConstraintsScope  Canvas :androidx.compose.foundation.layout.BoxWithConstraintsScope  Color :androidx.compose.foundation.layout.BoxWithConstraintsScope  FaceOverlay :androidx.compose.foundation.layout.BoxWithConstraintsScope  
FontWeight :androidx.compose.foundation.layout.BoxWithConstraintsScope  
MaterialTheme :androidx.compose.foundation.layout.BoxWithConstraintsScope  Modifier :androidx.compose.foundation.layout.BoxWithConstraintsScope  Offset :androidx.compose.foundation.layout.BoxWithConstraintsScope  Size :androidx.compose.foundation.layout.BoxWithConstraintsScope  Stroke :androidx.compose.foundation.layout.BoxWithConstraintsScope  Text :androidx.compose.foundation.layout.BoxWithConstraintsScope  	TextAlign :androidx.compose.foundation.layout.BoxWithConstraintsScope  align :androidx.compose.foundation.layout.BoxWithConstraintsScope  center :androidx.compose.foundation.layout.BoxWithConstraintsScope  constraints :androidx.compose.foundation.layout.BoxWithConstraintsScope  dp :androidx.compose.foundation.layout.BoxWithConstraintsScope  fillMaxSize :androidx.compose.foundation.layout.BoxWithConstraintsScope  	maxHeight :androidx.compose.foundation.layout.BoxWithConstraintsScope  maxOf :androidx.compose.foundation.layout.BoxWithConstraintsScope  maxWidth :androidx.compose.foundation.layout.BoxWithConstraintsScope  padding :androidx.compose.foundation.layout.BoxWithConstraintsScope  AnalysisResultCard .androidx.compose.foundation.layout.ColumnScope  	Analytics .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Face .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Image .androidx.compose.foundation.layout.ColumnScope  
InputImage .androidx.compose.foundation.layout.ColumnScope  LayerToggleButton .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Log .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  TwelvePalacesData .androidx.compose.foundation.layout.ColumnScope  
Visibility .androidx.compose.foundation.layout.ColumnScope  	associate .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  first .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  
plusAssign .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  to .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  with .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Brush +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  FlipCameraAndroid +androidx.compose.foundation.layout.RowScope  FloatingActionButton +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  Log +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  PhotoLibrary +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  radialGradient +androidx.compose.foundation.layout.RowScope  shadow +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  AnalysisResultCard .androidx.compose.foundation.lazy.LazyItemScope  Button .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  TwelvePalacesData .androidx.compose.foundation.lazy.LazyItemScope  	associate .androidx.compose.foundation.lazy.LazyItemScope  launch .androidx.compose.foundation.lazy.LazyItemScope  
plusAssign .androidx.compose.foundation.lazy.LazyItemScope  to .androidx.compose.foundation.lazy.LazyItemScope  AnalysisResultCard .androidx.compose.foundation.lazy.LazyListScope  Button .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  TwelvePalacesData .androidx.compose.foundation.lazy.LazyListScope  	associate .androidx.compose.foundation.lazy.LazyListScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  launch .androidx.compose.foundation.lazy.LazyListScope  
plusAssign .androidx.compose.foundation.lazy.LazyListScope  to .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  	ArrowBack 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  	Analytics ,androidx.compose.material.icons.Icons.Filled  Face ,androidx.compose.material.icons.Icons.Filled  FlipCameraAndroid ,androidx.compose.material.icons.Icons.Filled  Image ,androidx.compose.material.icons.Icons.Filled  PhotoLibrary ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  
Visibility ,androidx.compose.material.icons.Icons.Filled  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  	Analytics &androidx.compose.material.icons.filled  Face &androidx.compose.material.icons.filled  FlipCameraAndroid &androidx.compose.material.icons.filled  Image &androidx.compose.material.icons.filled  PhotoLibrary &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  
Visibility &androidx.compose.material.icons.filled  
VisibilityOff &androidx.compose.material.icons.filled  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
AsyncImage androidx.compose.material3  Bitmap androidx.compose.material3  Box androidx.compose.material3  BoxWithConstraints androidx.compose.material3  Brush androidx.compose.material3  Build androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  
CameraView androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  ContentScale androidx.compose.material3  Context androidx.compose.material3  	Exception androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FaceAnalysisScreen androidx.compose.material3  FaceDetectionApp androidx.compose.material3  FaceMesh androidx.compose.material3  FaceOverlay androidx.compose.material3  	FaceTheme androidx.compose.material3  Float androidx.compose.material3  FloatingActionButton androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  
ImageUtils androidx.compose.material3  
InputImage androidx.compose.material3  LaunchedEffect androidx.compose.material3  List androidx.compose.material3  Log androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedTextField androidx.compose.material3  PalaceAnalysisResult androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  PermissionUtils androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  SettingsScreen androidx.compose.material3  Suppress androidx.compose.material3  Text androidx.compose.material3  	TopAppBar androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  Uri androidx.compose.material3  View androidx.compose.material3  WindowCompat androidx.compose.material3  WindowInsets androidx.compose.material3  WindowInsetsController androidx.compose.material3  
WindowManager androidx.compose.material3  align androidx.compose.material3  androidx androidx.compose.material3  
background androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  first androidx.compose.material3  forEach androidx.compose.material3  getBitmapFromUri androidx.compose.material3  getPermissionsToRequest androidx.compose.material3  getValue androidx.compose.material3  
isNotEmpty androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  minOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  radialGradient androidx.compose.material3  remember androidx.compose.material3  setValue androidx.compose.material3  shadow androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  with androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  
bodyMedium %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  
titleSmall %androidx.compose.material3.Typography  ActivityResultContracts androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  AnalysisResultCard androidx.compose.runtime  AndroidView androidx.compose.runtime  Arrangement androidx.compose.runtime  
AsyncImage androidx.compose.runtime  Bitmap androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  BoxWithConstraints androidx.compose.runtime  Brush androidx.compose.runtime  Build androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  CameraControls androidx.compose.runtime  CameraSelector androidx.compose.runtime  
CameraView androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  ContentScale androidx.compose.runtime  Context androidx.compose.runtime  
ContextCompat androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  	Exception androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FaceAnalysisScreen androidx.compose.runtime  FaceDetectionApp androidx.compose.runtime  FaceDetectionGuide androidx.compose.runtime  FaceMesh androidx.compose.runtime  FaceOverlay androidx.compose.runtime  	FaceTheme androidx.compose.runtime  Float androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Image androidx.compose.runtime  
ImageUtils androidx.compose.runtime  
InputImage androidx.compose.runtime  Int androidx.compose.runtime  IntSize androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  LayerControlPanel androidx.compose.runtime  
LazyColumn androidx.compose.runtime  List androidx.compose.runtime  LocalConfiguration androidx.compose.runtime  Log androidx.compose.runtime  Manifest androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PackageManager androidx.compose.runtime  PalaceAnalysisResult androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  PermissionUtils androidx.compose.runtime  Preview androidx.compose.runtime  PreviewView androidx.compose.runtime  ProcessCameraProvider androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Scaffold androidx.compose.runtime  ScaleAndOffsetResult androidx.compose.runtime  SettingsScreen androidx.compose.runtime  SimpleCameraPreview androidx.compose.runtime  Spacer androidx.compose.runtime  Suppress androidx.compose.runtime  Text androidx.compose.runtime  	TopAppBar androidx.compose.runtime  TwelvePalacesData androidx.compose.runtime  Unit androidx.compose.runtime  Uri androidx.compose.runtime  View androidx.compose.runtime  WindowCompat androidx.compose.runtime  WindowInsets androidx.compose.runtime  WindowInsetsController androidx.compose.runtime  
WindowManager androidx.compose.runtime  align androidx.compose.runtime  also androidx.compose.runtime  androidx androidx.compose.runtime  apply androidx.compose.runtime  
asImageBitmap androidx.compose.runtime  	associate androidx.compose.runtime  
background androidx.compose.runtime  calculateScaleAndOffset androidx.compose.runtime  
cardColors androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  first androidx.compose.runtime  forEach androidx.compose.runtime  getBitmapFromUri androidx.compose.runtime  getPermissionsToRequest androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  heightIn androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  minOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  onGloballyPositioned androidx.compose.runtime  padding androidx.compose.runtime  
plusAssign androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  spacedBy androidx.compose.runtime  to androidx.compose.runtime  verticalGradient androidx.compose.runtime  weight androidx.compose.runtime  with androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  	onDispose .androidx.compose.runtime.DisposableEffectScope  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  	TopCenter androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  TopStart androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	TopCenter 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  TopStart 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  onGloballyPositioned androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  scale androidx.compose.ui.Modifier  shadow androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  scale &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  scale androidx.compose.ui.draw  shadow androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  center androidx.compose.ui.geometry  	Companion #androidx.compose.ui.geometry.Offset  Zero #androidx.compose.ui.geometry.Offset  let #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  Zero -androidx.compose.ui.geometry.Offset.Companion  center !androidx.compose.ui.geometry.Size  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Brush androidx.compose.ui.graphics  Canvas androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  ImageBitmap androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  	PointMode androidx.compose.ui.graphics  	StrokeCap androidx.compose.ui.graphics  
StrokeJoin androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  nativeCanvas androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  nativeCanvas #androidx.compose.ui.graphics.Canvas  Black "androidx.compose.ui.graphics.Color  Blue "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Cyan "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Magenta "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Blue ,androidx.compose.ui.graphics.Color.Companion  Cyan ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Magenta ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  close !androidx.compose.ui.graphics.Path  lineTo !androidx.compose.ui.graphics.Path  moveTo !androidx.compose.ui.graphics.Path  quadraticTo !androidx.compose.ui.graphics.Path  	Companion &androidx.compose.ui.graphics.PointMode  Points &androidx.compose.ui.graphics.PointMode  Points 0androidx.compose.ui.graphics.PointMode.Companion  	Companion &androidx.compose.ui.graphics.StrokeCap  Round &androidx.compose.ui.graphics.StrokeCap  Round 0androidx.compose.ui.graphics.StrokeCap.Companion  	Companion 'androidx.compose.ui.graphics.StrokeJoin  Round 'androidx.compose.ui.graphics.StrokeJoin  Round 1androidx.compose.ui.graphics.StrokeJoin.Companion  DrawContext &androidx.compose.ui.graphics.drawscope  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  canvas 2androidx.compose.ui.graphics.drawscope.DrawContext  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  Path 0androidx.compose.ui.graphics.drawscope.DrawScope  	PointMode 0androidx.compose.ui.graphics.drawscope.DrawScope  Size 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  	StrokeCap 0androidx.compose.ui.graphics.drawscope.DrawScope  
StrokeJoin 0androidx.compose.ui.graphics.drawscope.DrawScope  
TwelvePalaces 0androidx.compose.ui.graphics.drawscope.DrawScope  android 0androidx.compose.ui.graphics.drawscope.DrawScope  apply 0androidx.compose.ui.graphics.drawscope.DrawScope  calculatePalacePositions 0androidx.compose.ui.graphics.drawscope.DrawScope  center 0androidx.compose.ui.graphics.drawscope.DrawScope  coerceAtMost 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  drawArc 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawContext 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFace3DPoints 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFaceContour 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFaceMeshPoints 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFacePoints 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFacialFeatures 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  drawPath 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawPoints 0androidx.compose.ui.graphics.drawscope.DrawScope  drawTwelvePalaces 0androidx.compose.ui.graphics.drawscope.DrawScope  drop 0androidx.compose.ui.graphics.drawscope.DrawScope  first 0androidx.compose.ui.graphics.drawscope.DrawScope  forEachIndexed 0androidx.compose.ui.graphics.drawscope.DrawScope  getCircledNumber 0androidx.compose.ui.graphics.drawscope.DrawScope  let 0androidx.compose.ui.graphics.drawscope.DrawScope  listOf 0androidx.compose.ui.graphics.drawscope.DrawScope  map 0androidx.compose.ui.graphics.drawscope.DrawScope  
mapNotNull 0androidx.compose.ui.graphics.drawscope.DrawScope  nativeCanvas 0androidx.compose.ui.graphics.drawscope.DrawScope  
plusAssign 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  sp 0androidx.compose.ui.graphics.drawscope.DrawScope  toArgb 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  until 0androidx.compose.ui.graphics.drawscope.DrawScope  ImageVector #androidx.compose.ui.graphics.vector  ContentScale androidx.compose.ui.layout  LayoutCoordinates androidx.compose.ui.layout  onGloballyPositioned androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Crop 'androidx.compose.ui.layout.ContentScale  Fit 'androidx.compose.ui.layout.ContentScale  Crop 1androidx.compose.ui.layout.ContentScale.Companion  Fit 1androidx.compose.ui.layout.ContentScale.Companion  localToRoot ,androidx.compose.ui.layout.LayoutCoordinates  size ,androidx.compose.ui.layout.LayoutCoordinates  LocalConfiguration androidx.compose.ui.platform  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  PasswordVisualTransformation androidx.compose.ui.text.input  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Constraints androidx.compose.ui.unit  Dp androidx.compose.ui.unit  IntSize androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  	maxHeight $androidx.compose.ui.unit.Constraints  maxWidth $androidx.compose.ui.unit.Constraints  toPx  androidx.compose.ui.unit.Density  div androidx.compose.ui.unit.Dp  minus androidx.compose.ui.unit.Dp  times androidx.compose.ui.unit.Dp  toPx androidx.compose.ui.unit.Dp  	Companion  androidx.compose.ui.unit.IntSize  Zero  androidx.compose.ui.unit.IntSize  height  androidx.compose.ui.unit.IntSize  width  androidx.compose.ui.unit.IntSize  Zero *androidx.compose.ui.unit.IntSize.Companion  toPx !androidx.compose.ui.unit.TextUnit  AndroidView androidx.compose.ui.viewinterop  ActivityResultContracts #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  FaceDetectionApp #androidx.core.app.ComponentActivity  	FaceTheme #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  PermissionUtils #androidx.core.app.ComponentActivity  SimpleCameraPreview #androidx.core.app.ComponentActivity  Suppress #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  WindowInsets #androidx.core.app.ComponentActivity  WindowInsetsController #androidx.core.app.ComponentActivity  
WindowManager #androidx.core.app.ComponentActivity  getPermissionsToRequest #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  WindowCompat androidx.core.view  setDecorFitsSystemWindows androidx.core.view.WindowCompat  LifecycleOwner androidx.lifecycle  
AsyncImage coil.compose  
ChatChoice com.aallam.openai.api.chat  	ChatChunk com.aallam.openai.api.chat  ChatCompletion com.aallam.openai.api.chat  ChatCompletionChunk com.aallam.openai.api.chat  ChatCompletionRequest com.aallam.openai.api.chat  	ChatDelta com.aallam.openai.api.chat  ChatMessage com.aallam.openai.api.chat  ChatRole com.aallam.openai.api.chat  message %com.aallam.openai.api.chat.ChatChoice  delta $com.aallam.openai.api.chat.ChatChunk  choices )com.aallam.openai.api.chat.ChatCompletion  choices .com.aallam.openai.api.chat.ChatCompletionChunk  content $com.aallam.openai.api.chat.ChatDelta  content &com.aallam.openai.api.chat.ChatMessage  Role com.aallam.openai.api.core  	Companion com.aallam.openai.api.core.Role  System com.aallam.openai.api.core.Role  User com.aallam.openai.api.core.Role  System )com.aallam.openai.api.core.Role.Companion  User )com.aallam.openai.api.core.Role.Companion  Timeout com.aallam.openai.api.http  ModelId com.aallam.openai.api.model  OpenAI com.aallam.openai.client  
OpenAIHost com.aallam.openai.client  chatCompletion com.aallam.openai.client.Chat  chatCompletions com.aallam.openai.client.Chat  chatCompletion com.aallam.openai.client.OpenAI  chatCompletions com.aallam.openai.client.OpenAI  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  
InputImage com.google.mlkit.vision.common  
fromBitmap )com.google.mlkit.vision.common.InputImage  fromMediaImage )com.google.mlkit.vision.common.InputImage  height )com.google.mlkit.vision.common.InputImage  width )com.google.mlkit.vision.common.InputImage  x 'com.google.mlkit.vision.common.PointF3D  y 'com.google.mlkit.vision.common.PointF3D  z 'com.google.mlkit.vision.common.PointF3D  FaceMesh  com.google.mlkit.vision.facemesh  FaceMeshDetection  com.google.mlkit.vision.facemesh  FaceMeshDetector  com.google.mlkit.vision.facemesh  FaceMeshDetectorOptions  com.google.mlkit.vision.facemesh  
FaceMeshPoint  com.google.mlkit.vision.facemesh  	allPoints )com.google.mlkit.vision.facemesh.FaceMesh  boundingBox )com.google.mlkit.vision.facemesh.FaceMesh  	getClient 2com.google.mlkit.vision.facemesh.FaceMeshDetection  close 1com.google.mlkit.vision.facemesh.FaceMeshDetector  process 1com.google.mlkit.vision.facemesh.FaceMeshDetector  Builder 8com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions  	FACE_MESH 8com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions  build @com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions.Builder  
setUseCase @com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions.Builder  position .com.google.mlkit.vision.facemesh.FaceMeshPoint  ActivityResultContracts com.wendy.face  AlertDialog com.wendy.face  	Alignment com.wendy.face  AndroidView com.wendy.face  Arrangement com.wendy.face  
AsyncImage com.wendy.face  Bitmap com.wendy.face  Boolean com.wendy.face  Box com.wendy.face  BoxWithConstraints com.wendy.face  Build com.wendy.face  Bundle com.wendy.face  Button com.wendy.face  CameraSelector com.wendy.face  
CameraView com.wendy.face  Color com.wendy.face  Column com.wendy.face  ComponentActivity com.wendy.face  
Composable com.wendy.face  ContentScale com.wendy.face  
ContextCompat com.wendy.face  	Exception com.wendy.face  FaceAnalysisScreen com.wendy.face  FaceDetectionApp com.wendy.face  FaceMesh com.wendy.face  FaceOverlay com.wendy.face  	FaceTheme com.wendy.face  Float com.wendy.face  Icon com.wendy.face  
IconButton com.wendy.face  Icons com.wendy.face  
ImageUtils com.wendy.face  
InputImage com.wendy.face  LaunchedEffect com.wendy.face  List com.wendy.face  Log com.wendy.face  MainActivity com.wendy.face  Manifest com.wendy.face  
MaterialTheme com.wendy.face  Modifier com.wendy.face  OutlinedTextField com.wendy.face  PackageManager com.wendy.face  PalaceAnalysisResult com.wendy.face  PasswordVisualTransformation com.wendy.face  PermissionUtils com.wendy.face  Preview com.wendy.face  PreviewView com.wendy.face  ProcessCameraProvider com.wendy.face  SettingsScreen com.wendy.face  SimpleCameraPreview com.wendy.face  Suppress com.wendy.face  TestCameraActivity com.wendy.face  Text com.wendy.face  Unit com.wendy.face  Uri com.wendy.face  View com.wendy.face  WindowCompat com.wendy.face  WindowInsets com.wendy.face  WindowInsetsController com.wendy.face  
WindowManager com.wendy.face  align com.wendy.face  also com.wendy.face  androidx com.wendy.face  
background com.wendy.face  	emptyList com.wendy.face  fillMaxSize com.wendy.face  first com.wendy.face  forEach com.wendy.face  getBitmapFromUri com.wendy.face  getPermissionsToRequest com.wendy.face  getValue com.wendy.face  
isNotEmpty com.wendy.face  let com.wendy.face  minOf com.wendy.face  mutableStateOf com.wendy.face  padding com.wendy.face  provideDelegate com.wendy.face  remember com.wendy.face  setValue com.wendy.face  spacedBy com.wendy.face  VOLC_API_KEY com.wendy.face.BuildConfig  ActivityResultContracts com.wendy.face.MainActivity  Build com.wendy.face.MainActivity  FaceDetectionApp com.wendy.face.MainActivity  	FaceTheme com.wendy.face.MainActivity  Log com.wendy.face.MainActivity  PermissionUtils com.wendy.face.MainActivity  View com.wendy.face.MainActivity  WindowCompat com.wendy.face.MainActivity  WindowInsets com.wendy.face.MainActivity  WindowInsetsController com.wendy.face.MainActivity  
WindowManager com.wendy.face.MainActivity  getPermissionsToRequest com.wendy.face.MainActivity  
isNotEmpty com.wendy.face.MainActivity  let com.wendy.face.MainActivity  onImageSelected com.wendy.face.MainActivity  registerForActivityResult com.wendy.face.MainActivity  "requestMultiplePermissionsLauncher com.wendy.face.MainActivity  selectImageLauncher com.wendy.face.MainActivity  
setContent com.wendy.face.MainActivity  setupFullScreenMode com.wendy.face.MainActivity  window com.wendy.face.MainActivity  ActivityResultContracts !com.wendy.face.TestCameraActivity  
ContextCompat !com.wendy.face.TestCameraActivity  	FaceTheme !com.wendy.face.TestCameraActivity  Log !com.wendy.face.TestCameraActivity  Manifest !com.wendy.face.TestCameraActivity  PackageManager !com.wendy.face.TestCameraActivity  SimpleCameraPreview !com.wendy.face.TestCameraActivity  registerForActivityResult !com.wendy.face.TestCameraActivity  requestPermissionLauncher !com.wendy.face.TestCameraActivity  
setContent !com.wendy.face.TestCameraActivity  Double com.wendy.face.analyzer  FaceAnalyzer com.wendy.face.analyzer  FaceMesh com.wendy.face.analyzer  
FaceMeshPoint com.wendy.face.analyzer  FacePalaces com.wendy.face.analyzer  List com.wendy.face.analyzer  PalaceAnalysisResult com.wendy.face.analyzer  String com.wendy.face.analyzer  
TwelvePalaces com.wendy.face.analyzer  abs com.wendy.face.analyzer  average com.wendy.face.analyzer  flatten com.wendy.face.analyzer  forEachIndexed com.wendy.face.analyzer  getPalaceNameWithNumber com.wendy.face.analyzer  getPalacePoints com.wendy.face.analyzer  indices com.wendy.face.analyzer  
isNotEmpty com.wendy.face.analyzer  map com.wendy.face.analyzer  
mutableListOf com.wendy.face.analyzer  
plusAssign com.wendy.face.analyzer  FacePalaces $com.wendy.face.analyzer.FaceAnalyzer  PalaceAnalysisResult $com.wendy.face.analyzer.FaceAnalyzer  
TwelvePalaces $com.wendy.face.analyzer.FaceAnalyzer  abs $com.wendy.face.analyzer.FaceAnalyzer  analyze $com.wendy.face.analyzer.FaceAnalyzer  average $com.wendy.face.analyzer.FaceAnalyzer  
calculateArea $com.wendy.face.analyzer.FaceAnalyzer  calculateFullness $com.wendy.face.analyzer.FaceAnalyzer  flatten $com.wendy.face.analyzer.FaceAnalyzer  forEachIndexed $com.wendy.face.analyzer.FaceAnalyzer  generateDescription $com.wendy.face.analyzer.FaceAnalyzer  getPalaceNameWithNumber $com.wendy.face.analyzer.FaceAnalyzer  getPalacePoints $com.wendy.face.analyzer.FaceAnalyzer  indices $com.wendy.face.analyzer.FaceAnalyzer  
isNotEmpty $com.wendy.face.analyzer.FaceAnalyzer  map $com.wendy.face.analyzer.FaceAnalyzer  
mutableListOf $com.wendy.face.analyzer.FaceAnalyzer  
plusAssign $com.wendy.face.analyzer.FaceAnalyzer  description ,com.wendy.face.analyzer.PalaceAnalysisResult  
palaceName ,com.wendy.face.analyzer.PalaceAnalysisResult  Bitmap com.wendy.face.camera  Boolean com.wendy.face.camera  
CameraManager com.wendy.face.camera  CameraSelector com.wendy.face.camera  Context com.wendy.face.camera  
ContextCompat com.wendy.face.camera  	Exception com.wendy.face.camera  	Executors com.wendy.face.camera  FaceDetectorManager com.wendy.face.camera  FaceMesh com.wendy.face.camera  
ImageAnalysis com.wendy.face.camera  ImageCapture com.wendy.face.camera  ImageCaptureException com.wendy.face.camera  
ImageProxy com.wendy.face.camera  
ImageUtils com.wendy.face.camera  
InputImage com.wendy.face.camera  Int com.wendy.face.camera  LifecycleOwner com.wendy.face.camera  List com.wendy.face.camera  Locale com.wendy.face.camera  Log com.wendy.face.camera  Preview com.wendy.face.camera  PreviewView com.wendy.face.camera  ProcessCameraProvider com.wendy.face.camera  SimpleDateFormat com.wendy.face.camera  SuppressLint com.wendy.face.camera  System com.wendy.face.camera  TAG com.wendy.face.camera  Unit com.wendy.face.camera  Uri com.wendy.face.camera  also com.wendy.face.camera  android com.wendy.face.camera  apply com.wendy.face.camera  context com.wendy.face.camera  currentIsBackCamera com.wendy.face.camera  imageProxyToBitmap com.wendy.face.camera  saveBitmapToGallery com.wendy.face.camera  Bitmap #com.wendy.face.camera.CameraManager  Boolean #com.wendy.face.camera.CameraManager  CameraSelector #com.wendy.face.camera.CameraManager  Context #com.wendy.face.camera.CameraManager  
ContextCompat #com.wendy.face.camera.CameraManager  	Exception #com.wendy.face.camera.CameraManager  	Executors #com.wendy.face.camera.CameraManager  FaceDetectorManager #com.wendy.face.camera.CameraManager  FaceMesh #com.wendy.face.camera.CameraManager  
ImageAnalysis #com.wendy.face.camera.CameraManager  ImageCapture #com.wendy.face.camera.CameraManager  ImageCaptureException #com.wendy.face.camera.CameraManager  
ImageProxy #com.wendy.face.camera.CameraManager  
ImageUtils #com.wendy.face.camera.CameraManager  
InputImage #com.wendy.face.camera.CameraManager  Int #com.wendy.face.camera.CameraManager  LifecycleOwner #com.wendy.face.camera.CameraManager  List #com.wendy.face.camera.CameraManager  Locale #com.wendy.face.camera.CameraManager  Log #com.wendy.face.camera.CameraManager  Preview #com.wendy.face.camera.CameraManager  PreviewView #com.wendy.face.camera.CameraManager  ProcessCameraProvider #com.wendy.face.camera.CameraManager  SimpleDateFormat #com.wendy.face.camera.CameraManager  SuppressLint #com.wendy.face.camera.CameraManager  System #com.wendy.face.camera.CameraManager  TAG #com.wendy.face.camera.CameraManager  Unit #com.wendy.face.camera.CameraManager  Uri #com.wendy.face.camera.CameraManager  also #com.wendy.face.camera.CameraManager  android #com.wendy.face.camera.CameraManager  apply #com.wendy.face.camera.CameraManager  
bindCamera #com.wendy.face.camera.CameraManager  cameraExecutor #com.wendy.face.camera.CameraManager  cameraProvider #com.wendy.face.camera.CameraManager  cameraProviderFuture #com.wendy.face.camera.CameraManager  context #com.wendy.face.camera.CameraManager  currentIsBackCamera #com.wendy.face.camera.CameraManager  faceDetectorManager #com.wendy.face.camera.CameraManager  imageCapture #com.wendy.face.camera.CameraManager  imageProxyToBitmap #com.wendy.face.camera.CameraManager  lifecycleOwner #com.wendy.face.camera.CameraManager  release #com.wendy.face.camera.CameraManager  saveBitmapToGallery #com.wendy.face.camera.CameraManager  takePicture #com.wendy.face.camera.CameraManager  Bitmap -com.wendy.face.camera.CameraManager.Companion  CameraSelector -com.wendy.face.camera.CameraManager.Companion  
ContextCompat -com.wendy.face.camera.CameraManager.Companion  	Executors -com.wendy.face.camera.CameraManager.Companion  FaceDetectorManager -com.wendy.face.camera.CameraManager.Companion  
ImageAnalysis -com.wendy.face.camera.CameraManager.Companion  ImageCapture -com.wendy.face.camera.CameraManager.Companion  
ImageUtils -com.wendy.face.camera.CameraManager.Companion  
InputImage -com.wendy.face.camera.CameraManager.Companion  Locale -com.wendy.face.camera.CameraManager.Companion  Log -com.wendy.face.camera.CameraManager.Companion  Preview -com.wendy.face.camera.CameraManager.Companion  ProcessCameraProvider -com.wendy.face.camera.CameraManager.Companion  SimpleDateFormat -com.wendy.face.camera.CameraManager.Companion  System -com.wendy.face.camera.CameraManager.Companion  TAG -com.wendy.face.camera.CameraManager.Companion  also -com.wendy.face.camera.CameraManager.Companion  android -com.wendy.face.camera.CameraManager.Companion  apply -com.wendy.face.camera.CameraManager.Companion  context -com.wendy.face.camera.CameraManager.Companion  currentIsBackCamera -com.wendy.face.camera.CameraManager.Companion  imageProxyToBitmap -com.wendy.face.camera.CameraManager.Companion  saveBitmapToGallery -com.wendy.face.camera.CameraManager.Companion  OnImageCapturedCallback 0com.wendy.face.camera.CameraManager.ImageCapture  OnImageCapturedCallback "com.wendy.face.camera.ImageCapture  Boolean com.wendy.face.detection  Color com.wendy.face.detection  DetectionFrame com.wendy.face.detection  	DrawScope com.wendy.face.detection  	Exception com.wendy.face.detection  FaceDetectionSmoother com.wendy.face.detection  FaceDetectorManager com.wendy.face.detection  FaceMesh com.wendy.face.detection  FaceMeshDetection com.wendy.face.detection  FaceMeshDetectorOptions com.wendy.face.detection  FaceRenderer com.wendy.face.detection  Float com.wendy.face.detection  
InputImage com.wendy.face.detection  Int com.wendy.face.detection  List com.wendy.face.detection  Log com.wendy.face.detection  Long com.wendy.face.detection  MAX_HISTORY_SIZE com.wendy.face.detection  MAX_MOVEMENT_THRESHOLD com.wendy.face.detection  Mutex com.wendy.face.detection  Offset com.wendy.face.detection  System com.wendy.face.detection  TAG com.wendy.face.detection  Unit com.wendy.face.detection  abs com.wendy.face.detection  	emptyList com.wendy.face.detection  forEach com.wendy.face.detection  forEachIndexed com.wendy.face.detection  indices com.wendy.face.detection  
mutableListOf com.wendy.face.detection  sqrt com.wendy.face.detection  with com.wendy.face.detection  withLock com.wendy.face.detection  Boolean .com.wendy.face.detection.FaceDetectionSmoother  DetectionFrame .com.wendy.face.detection.FaceDetectionSmoother  FaceMesh .com.wendy.face.detection.FaceDetectionSmoother  Int .com.wendy.face.detection.FaceDetectionSmoother  List .com.wendy.face.detection.FaceDetectionSmoother  Log .com.wendy.face.detection.FaceDetectionSmoother  Long .com.wendy.face.detection.FaceDetectionSmoother  MAX_HISTORY_SIZE .com.wendy.face.detection.FaceDetectionSmoother  MAX_MOVEMENT_THRESHOLD .com.wendy.face.detection.FaceDetectionSmoother  Mutex .com.wendy.face.detection.FaceDetectionSmoother  System .com.wendy.face.detection.FaceDetectionSmoother  TAG .com.wendy.face.detection.FaceDetectionSmoother  abs .com.wendy.face.detection.FaceDetectionSmoother  detectionHistory .com.wendy.face.detection.FaceDetectionSmoother  	emptyList .com.wendy.face.detection.FaceDetectionSmoother  indices .com.wendy.face.detection.FaceDetectionSmoother  isDetectionStable .com.wendy.face.detection.FaceDetectionSmoother  isFaceMovedTooMuch .com.wendy.face.detection.FaceDetectionSmoother  lastValidDetection .com.wendy.face.detection.FaceDetectionSmoother  
mutableListOf .com.wendy.face.detection.FaceDetectionSmoother  mutex .com.wendy.face.detection.FaceDetectionSmoother  sqrt .com.wendy.face.detection.FaceDetectionSmoother  withLock .com.wendy.face.detection.FaceDetectionSmoother  DetectionFrame 8com.wendy.face.detection.FaceDetectionSmoother.Companion  Log 8com.wendy.face.detection.FaceDetectionSmoother.Companion  MAX_HISTORY_SIZE 8com.wendy.face.detection.FaceDetectionSmoother.Companion  MAX_MOVEMENT_THRESHOLD 8com.wendy.face.detection.FaceDetectionSmoother.Companion  Mutex 8com.wendy.face.detection.FaceDetectionSmoother.Companion  System 8com.wendy.face.detection.FaceDetectionSmoother.Companion  TAG 8com.wendy.face.detection.FaceDetectionSmoother.Companion  abs 8com.wendy.face.detection.FaceDetectionSmoother.Companion  	emptyList 8com.wendy.face.detection.FaceDetectionSmoother.Companion  indices 8com.wendy.face.detection.FaceDetectionSmoother.Companion  
mutableListOf 8com.wendy.face.detection.FaceDetectionSmoother.Companion  sqrt 8com.wendy.face.detection.FaceDetectionSmoother.Companion  withLock 8com.wendy.face.detection.FaceDetectionSmoother.Companion  
faceMeshes =com.wendy.face.detection.FaceDetectionSmoother.DetectionFrame  	timestamp =com.wendy.face.detection.FaceDetectionSmoother.DetectionFrame  	Exception ,com.wendy.face.detection.FaceDetectorManager  FaceMesh ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetection ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetectorOptions ,com.wendy.face.detection.FaceDetectorManager  
InputImage ,com.wendy.face.detection.FaceDetectorManager  Int ,com.wendy.face.detection.FaceDetectorManager  List ,com.wendy.face.detection.FaceDetectorManager  Log ,com.wendy.face.detection.FaceDetectorManager  TAG ,com.wendy.face.detection.FaceDetectorManager  Unit ,com.wendy.face.detection.FaceDetectorManager  detectFaces ,com.wendy.face.detection.FaceDetectorManager  faceMeshDetector ,com.wendy.face.detection.FaceDetectorManager  faceMeshDetectorOptions ,com.wendy.face.detection.FaceDetectorManager  forEachIndexed ,com.wendy.face.detection.FaceDetectorManager  release ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetection 6com.wendy.face.detection.FaceDetectorManager.Companion  FaceMeshDetectorOptions 6com.wendy.face.detection.FaceDetectorManager.Companion  Log 6com.wendy.face.detection.FaceDetectorManager.Companion  TAG 6com.wendy.face.detection.FaceDetectorManager.Companion  forEachIndexed 6com.wendy.face.detection.FaceDetectorManager.Companion  Color %com.wendy.face.detection.FaceRenderer  Offset %com.wendy.face.detection.FaceRenderer  dp %com.wendy.face.detection.FaceRenderer  drawFaceMeshPoints %com.wendy.face.detection.FaceRenderer  with %com.wendy.face.detection.FaceRenderer  ChatCompletion com.wendy.face.llm  ChatCompletionChunk com.wendy.face.llm  ChatCompletionRequest com.wendy.face.llm  ChatMessage com.wendy.face.llm  ChatRole com.wendy.face.llm  ContentNegotiation com.wendy.face.llm  	Exception com.wendy.face.llm  Flow com.wendy.face.llm  Json com.wendy.face.llm  
LLMService com.wendy.face.llm  Log com.wendy.face.llm  ModelId com.wendy.face.llm  OpenAI com.wendy.face.llm  
OpenAIHost com.wendy.face.llm  String com.wendy.face.llm  Timeout com.wendy.face.llm  TwelvePalacesData com.wendy.face.llm  android com.wendy.face.llm  catch com.wendy.face.llm  com com.wendy.face.llm  firstOrNull com.wendy.face.llm  flow com.wendy.face.llm  forEach com.wendy.face.llm  getDestinyNonStreaming com.wendy.face.llm  joinToString com.wendy.face.llm  kotlinx com.wendy.face.llm  listOf com.wendy.face.llm  map com.wendy.face.llm  openAI com.wendy.face.llm  orEmpty com.wendy.face.llm  
trimIndent com.wendy.face.llm  ChatCompletionRequest com.wendy.face.llm.LLMService  ChatMessage com.wendy.face.llm.LLMService  ChatRole com.wendy.face.llm.LLMService  ContentNegotiation com.wendy.face.llm.LLMService  Json com.wendy.face.llm.LLMService  Log com.wendy.face.llm.LLMService  ModelId com.wendy.face.llm.LLMService  OpenAI com.wendy.face.llm.LLMService  
OpenAIHost com.wendy.face.llm.LLMService  Timeout com.wendy.face.llm.LLMService  android com.wendy.face.llm.LLMService  catch com.wendy.face.llm.LLMService  com com.wendy.face.llm.LLMService  emitAll com.wendy.face.llm.LLMService  firstOrNull com.wendy.face.llm.LLMService  flow com.wendy.face.llm.LLMService  forEach com.wendy.face.llm.LLMService  
getDestiny com.wendy.face.llm.LLMService  getDestinyNonStreaming com.wendy.face.llm.LLMService  joinToString com.wendy.face.llm.LLMService  json com.wendy.face.llm.LLMService  kotlinx com.wendy.face.llm.LLMService  listOf com.wendy.face.llm.LLMService  map com.wendy.face.llm.LLMService  openAI com.wendy.face.llm.LLMService  orEmpty com.wendy.face.llm.LLMService  seconds com.wendy.face.llm.LLMService  
trimIndent com.wendy.face.llm.LLMService  
FaceMeshPoint com.wendy.face.model  FacePalaces com.wendy.face.model  Int com.wendy.face.model  
LinkedHashMap com.wendy.face.model  List com.wendy.face.model  Map com.wendy.face.model  Offset com.wendy.face.model  Pair com.wendy.face.model  String com.wendy.face.model  
TwelvePalaces com.wendy.face.model  TwelvePalacesData com.wendy.face.model  arrayOf com.wendy.face.model  average com.wendy.face.model  
component1 com.wendy.face.model  
component2 com.wendy.face.model  	emptyList com.wendy.face.model  forEach com.wendy.face.model  	getOrNull com.wendy.face.model  
isNotEmpty com.wendy.face.model  linkedMapOf com.wendy.face.model  listOf com.wendy.face.model  map com.wendy.face.model  
mapNotNull com.wendy.face.model  
mutableListOf com.wendy.face.model  to com.wendy.face.model  
FaceMeshPoint  com.wendy.face.model.FacePalaces  Int  com.wendy.face.model.FacePalaces  
LinkedHashMap  com.wendy.face.model.FacePalaces  List  com.wendy.face.model.FacePalaces  String  com.wendy.face.model.FacePalaces  	emptyList  com.wendy.face.model.FacePalaces  	getOrNull  com.wendy.face.model.FacePalaces  getPalacePoints  com.wendy.face.model.FacePalaces  linkedMapOf  com.wendy.face.model.FacePalaces  listOf  com.wendy.face.model.FacePalaces  map  com.wendy.face.model.FacePalaces  
mapNotNull  com.wendy.face.model.FacePalaces  
palaceIndices  com.wendy.face.model.FacePalaces  to  com.wendy.face.model.FacePalaces  FacePalaces "com.wendy.face.model.TwelvePalaces  Offset "com.wendy.face.model.TwelvePalaces  Pair "com.wendy.face.model.TwelvePalaces  arrayOf "com.wendy.face.model.TwelvePalaces  average "com.wendy.face.model.TwelvePalaces  calculatePalacePositions "com.wendy.face.model.TwelvePalaces  
component1 "com.wendy.face.model.TwelvePalaces  
component2 "com.wendy.face.model.TwelvePalaces  	emptyList "com.wendy.face.model.TwelvePalaces  getCircledNumber "com.wendy.face.model.TwelvePalaces  	getOrNull "com.wendy.face.model.TwelvePalaces  getPalaceNameWithNumber "com.wendy.face.model.TwelvePalaces  
isNotEmpty "com.wendy.face.model.TwelvePalaces  listOf "com.wendy.face.model.TwelvePalaces  map "com.wendy.face.model.TwelvePalaces  
mapNotNull "com.wendy.face.model.TwelvePalaces  
mutableListOf "com.wendy.face.model.TwelvePalaces  palaceNames "com.wendy.face.model.TwelvePalaces  palaces &com.wendy.face.model.TwelvePalacesData  	Alignment com.wendy.face.ui.components  AndroidView com.wendy.face.ui.components  Arrangement com.wendy.face.ui.components  
AsyncImage com.wendy.face.ui.components  Bitmap com.wendy.face.ui.components  Boolean com.wendy.face.ui.components  Box com.wendy.face.ui.components  Brush com.wendy.face.ui.components  CameraControls com.wendy.face.ui.components  
CameraView com.wendy.face.ui.components  Canvas com.wendy.face.ui.components  CircleShape com.wendy.face.ui.components  Color com.wendy.face.ui.components  
Composable com.wendy.face.ui.components  ContentScale com.wendy.face.ui.components  DisposableEffect com.wendy.face.ui.components  	DrawScope com.wendy.face.ui.components  Face3DPointsDisplay com.wendy.face.ui.components  FaceDetectionGuide com.wendy.face.ui.components  FaceMesh com.wendy.face.ui.components  FaceOverlay com.wendy.face.ui.components  Float com.wendy.face.ui.components  FloatingActionButton com.wendy.face.ui.components  
FontWeight com.wendy.face.ui.components  Icon com.wendy.face.ui.components  
IconButton com.wendy.face.ui.components  Icons com.wendy.face.ui.components  ImageVector com.wendy.face.ui.components  Int com.wendy.face.ui.components  LayerControlPanel com.wendy.face.ui.components  LayerToggleButton com.wendy.face.ui.components  List com.wendy.face.ui.components  Log com.wendy.face.ui.components  
MaterialTheme com.wendy.face.ui.components  MirrorableAsyncImage com.wendy.face.ui.components  Modifier com.wendy.face.ui.components  Offset com.wendy.face.ui.components  Path com.wendy.face.ui.components  	PointMode com.wendy.face.ui.components  PreviewView com.wendy.face.ui.components  Row com.wendy.face.ui.components  Size com.wendy.face.ui.components  String com.wendy.face.ui.components  Stroke com.wendy.face.ui.components  	StrokeCap com.wendy.face.ui.components  
StrokeJoin com.wendy.face.ui.components  Text com.wendy.face.ui.components  	TextAlign com.wendy.face.ui.components  
TwelvePalaces com.wendy.face.ui.components  Unit com.wendy.face.ui.components  Uri com.wendy.face.ui.components  align com.wendy.face.ui.components  android com.wendy.face.ui.components  apply com.wendy.face.ui.components  
background com.wendy.face.ui.components  calculatePalacePositions com.wendy.face.ui.components  coerceAtMost com.wendy.face.ui.components  drawFace3DPoints com.wendy.face.ui.components  drawFaceContour com.wendy.face.ui.components  drawFacePoints com.wendy.face.ui.components  drawFacialFeatures com.wendy.face.ui.components  drawTwelvePalaces com.wendy.face.ui.components  drop com.wendy.face.ui.components  fillMaxSize com.wendy.face.ui.components  fillMaxWidth com.wendy.face.ui.components  first com.wendy.face.ui.components  forEach com.wendy.face.ui.components  forEachIndexed com.wendy.face.ui.components  getCircledNumber com.wendy.face.ui.components  getValue com.wendy.face.ui.components  
isNotEmpty com.wendy.face.ui.components  let com.wendy.face.ui.components  listOf com.wendy.face.ui.components  map com.wendy.face.ui.components  
mapNotNull com.wendy.face.ui.components  maxOf com.wendy.face.ui.components  mutableStateOf com.wendy.face.ui.components  padding com.wendy.face.ui.components  
plusAssign com.wendy.face.ui.components  provideDelegate com.wendy.face.ui.components  radialGradient com.wendy.face.ui.components  remember com.wendy.face.ui.components  scale com.wendy.face.ui.components  setValue com.wendy.face.ui.components  shadow com.wendy.face.ui.components  size com.wendy.face.ui.components  spacedBy com.wendy.face.ui.components  toArgb com.wendy.face.ui.components  toPx com.wendy.face.ui.components  until com.wendy.face.ui.components  	Alignment com.wendy.face.ui.screens  AnalysisResultCard com.wendy.face.ui.screens  Arrangement com.wendy.face.ui.screens  Bitmap com.wendy.face.ui.screens  Boolean com.wendy.face.ui.screens  Box com.wendy.face.ui.screens  Brush com.wendy.face.ui.screens  Button com.wendy.face.ui.screens  Card com.wendy.face.ui.screens  CardDefaults com.wendy.face.ui.screens  Color com.wendy.face.ui.screens  Column com.wendy.face.ui.screens  
Composable com.wendy.face.ui.screens  ContentScale com.wendy.face.ui.screens  Context com.wendy.face.ui.screens  ExperimentalMaterial3Api com.wendy.face.ui.screens  FaceAnalysisScreen com.wendy.face.ui.screens  FaceMesh com.wendy.face.ui.screens  FaceOverlay com.wendy.face.ui.screens  Float com.wendy.face.ui.screens  
FontWeight com.wendy.face.ui.screens  Icon com.wendy.face.ui.screens  
IconButton com.wendy.face.ui.screens  Icons com.wendy.face.ui.screens  Image com.wendy.face.ui.screens  Int com.wendy.face.ui.screens  IntSize com.wendy.face.ui.screens  LayerControlPanel com.wendy.face.ui.screens  
LazyColumn com.wendy.face.ui.screens  List com.wendy.face.ui.screens  LocalConfiguration com.wendy.face.ui.screens  
MaterialTheme com.wendy.face.ui.screens  Modifier com.wendy.face.ui.screens  OptIn com.wendy.face.ui.screens  OutlinedTextField com.wendy.face.ui.screens  PalaceAnalysisResult com.wendy.face.ui.screens  RoundedCornerShape com.wendy.face.ui.screens  Scaffold com.wendy.face.ui.screens  ScaleAndOffsetResult com.wendy.face.ui.screens  SettingsScreen com.wendy.face.ui.screens  Spacer com.wendy.face.ui.screens  Text com.wendy.face.ui.screens  	TopAppBar com.wendy.face.ui.screens  TwelvePalacesData com.wendy.face.ui.screens  Unit com.wendy.face.ui.screens  Uri com.wendy.face.ui.screens  align com.wendy.face.ui.screens  androidx com.wendy.face.ui.screens  
asImageBitmap com.wendy.face.ui.screens  	associate com.wendy.face.ui.screens  
background com.wendy.face.ui.screens  calculateScaleAndOffset com.wendy.face.ui.screens  
cardColors com.wendy.face.ui.screens  fillMaxSize com.wendy.face.ui.screens  fillMaxWidth com.wendy.face.ui.screens  getValue com.wendy.face.ui.screens  height com.wendy.face.ui.screens  heightIn com.wendy.face.ui.screens  
isNotEmpty com.wendy.face.ui.screens  launch com.wendy.face.ui.screens  listOf com.wendy.face.ui.screens  mutableStateOf com.wendy.face.ui.screens  onGloballyPositioned com.wendy.face.ui.screens  padding com.wendy.face.ui.screens  
plusAssign com.wendy.face.ui.screens  provideDelegate com.wendy.face.ui.screens  remember com.wendy.face.ui.screens  rememberCoroutineScope com.wendy.face.ui.screens  setValue com.wendy.face.ui.screens  spacedBy com.wendy.face.ui.screens  to com.wendy.face.ui.screens  verticalGradient com.wendy.face.ui.screens  weight com.wendy.face.ui.screens  with com.wendy.face.ui.screens  offsetX .com.wendy.face.ui.screens.ScaleAndOffsetResult  offsetY .com.wendy.face.ui.screens.ScaleAndOffsetResult  scaleX .com.wendy.face.ui.screens.ScaleAndOffsetResult  scaleY .com.wendy.face.ui.screens.ScaleAndOffsetResult  Boolean com.wendy.face.ui.theme  Build com.wendy.face.ui.theme  
Composable com.wendy.face.ui.theme  DarkColorScheme com.wendy.face.ui.theme  	FaceTheme com.wendy.face.ui.theme  
FontFamily com.wendy.face.ui.theme  
FontWeight com.wendy.face.ui.theme  LightColorScheme com.wendy.face.ui.theme  Pink40 com.wendy.face.ui.theme  Pink80 com.wendy.face.ui.theme  Purple40 com.wendy.face.ui.theme  Purple80 com.wendy.face.ui.theme  PurpleGrey40 com.wendy.face.ui.theme  PurpleGrey80 com.wendy.face.ui.theme  
Typography com.wendy.face.ui.theme  Unit com.wendy.face.ui.theme  Array com.wendy.face.utils  Bitmap com.wendy.face.utils  
BitmapFactory com.wendy.face.utils  Boolean com.wendy.face.utils  	ByteArray com.wendy.face.utils  ContentResolver com.wendy.face.utils  Context com.wendy.face.utils  
ContextCompat com.wendy.face.utils  	Exception com.wendy.face.utils  
ExifInterface com.wendy.face.utils  Float com.wendy.face.utils  IOException com.wendy.face.utils  
ImageUtils com.wendy.face.utils  Int com.wendy.face.utils  List com.wendy.face.utils  Log com.wendy.face.utils  Manifest com.wendy.face.utils  Matrix com.wendy.face.utils  OutOfMemoryError com.wendy.face.utils  PackageManager com.wendy.face.utils  PermissionUtils com.wendy.face.utils  Rect com.wendy.face.utils  String com.wendy.face.utils  Uri com.wendy.face.utils  android com.wendy.face.utils  androidx com.wendy.face.utils  apply com.wendy.face.utils  arrayOf com.wendy.face.utils  first com.wendy.face.utils  java com.wendy.face.utils  let com.wendy.face.utils  max com.wendy.face.utils  min com.wendy.face.utils  
mutableListOf com.wendy.face.utils  timesAssign com.wendy.face.utils  to com.wendy.face.utils  toTypedArray com.wendy.face.utils  use com.wendy.face.utils  Options "com.wendy.face.utils.BitmapFactory  Bitmap com.wendy.face.utils.ImageUtils  
BitmapFactory com.wendy.face.utils.ImageUtils  	ByteArray com.wendy.face.utils.ImageUtils  
ExifInterface com.wendy.face.utils.ImageUtils  Log com.wendy.face.utils.ImageUtils  Matrix com.wendy.face.utils.ImageUtils  TAG com.wendy.face.utils.ImageUtils  android com.wendy.face.utils.ImageUtils  apply com.wendy.face.utils.ImageUtils  arrayOf com.wendy.face.utils.ImageUtils  calculateInSampleSize com.wendy.face.utils.ImageUtils  first com.wendy.face.utils.ImageUtils  getBitmapFromUri com.wendy.face.utils.ImageUtils  imageProxyToBitmap com.wendy.face.utils.ImageUtils  java com.wendy.face.utils.ImageUtils  let com.wendy.face.utils.ImageUtils  max com.wendy.face.utils.ImageUtils  min com.wendy.face.utils.ImageUtils  saveBitmapToGallery com.wendy.face.utils.ImageUtils  timesAssign com.wendy.face.utils.ImageUtils  to com.wendy.face.utils.ImageUtils  use com.wendy.face.utils.ImageUtils  
ContextCompat $com.wendy.face.utils.PermissionUtils  Manifest $com.wendy.face.utils.PermissionUtils  PackageManager $com.wendy.face.utils.PermissionUtils  android $com.wendy.face.utils.PermissionUtils  getPermissionsToRequest $com.wendy.face.utils.PermissionUtils  hasCameraPermission $com.wendy.face.utils.PermissionUtils  hasStoragePermission $com.wendy.face.utils.PermissionUtils  
mutableListOf $com.wendy.face.utils.PermissionUtils  toTypedArray $com.wendy.face.utils.PermissionUtils  camera com.wendy.face.utils.androidx  core $com.wendy.face.utils.androidx.camera  
ImageProxy )com.wendy.face.utils.androidx.camera.core  HttpClientConfig io.ktor.client  ContentNegotiation io.ktor.client.HttpClientConfig  Json io.ktor.client.HttpClientConfig  install io.ktor.client.HttpClientConfig  json io.ktor.client.HttpClientConfig  ClientPlugin io.ktor.client.plugins.api  ContentNegotiation )io.ktor.client.plugins.contentnegotiation  ContentNegotiationConfig )io.ktor.client.plugins.contentnegotiation  Json Bio.ktor.client.plugins.contentnegotiation.ContentNegotiationConfig  json Bio.ktor.client.plugins.contentnegotiation.ContentNegotiationConfig  json "io.ktor.serialization.kotlinx.json  IOException java.io  InputStream java.io  OutputStream java.io  close java.io.InputStream  let java.io.InputStream  use java.io.InputStream  use java.io.OutputStream  Class 	java.lang  	Exception 	java.lang  OutOfMemoryError 	java.lang  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  
ByteBuffer java.nio  	remaining java.nio.Buffer  get java.nio.ByteBuffer  	remaining java.nio.ByteBuffer  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Bitmap 	java.util  Boolean 	java.util  CameraSelector 	java.util  Context 	java.util  
ContextCompat 	java.util  	Exception 	java.util  	Executors 	java.util  FaceDetectorManager 	java.util  FaceMesh 	java.util  
ImageAnalysis 	java.util  ImageCapture 	java.util  ImageCaptureException 	java.util  
ImageProxy 	java.util  
ImageUtils 	java.util  
InputImage 	java.util  Int 	java.util  LifecycleOwner 	java.util  
LinkedHashMap 	java.util  List 	java.util  Locale 	java.util  Log 	java.util  Preview 	java.util  PreviewView 	java.util  ProcessCameraProvider 	java.util  SimpleDateFormat 	java.util  SuppressLint 	java.util  System 	java.util  TAG 	java.util  Unit 	java.util  Uri 	java.util  also 	java.util  android 	java.util  apply 	java.util  context 	java.util  currentIsBackCamera 	java.util  imageProxyToBitmap 	java.util  saveBitmapToGallery 	java.util  OnImageCapturedCallback java.util.ImageCapture  get java.util.LinkedHashMap  keys java.util.LinkedHashMap  US java.util.Locale  Executor java.util.concurrent  	Executors java.util.concurrent  execute java.util.concurrent.Executor  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  Array kotlin  	ByteArray kotlin  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  Suppress kotlin  	Throwable kotlin  also kotlin  apply kotlin  arrayOf kotlin  let kotlin  map kotlin  to kotlin  use kotlin  with kotlin  get kotlin.Array  
isNotEmpty kotlin.Array  not kotlin.Boolean  size kotlin.ByteArray  toString kotlin.Char  	compareTo 
kotlin.Double  div 
kotlin.Double  minus 
kotlin.Double  plus 
kotlin.Double  
plusAssign 
kotlin.Double  sp 
kotlin.Double  times 
kotlin.Double  toFloat 
kotlin.Double  coerceAtMost kotlin.Float  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  times kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  invoke kotlin.Function0  invoke kotlin.Function1  invoke kotlin.Function2  invoke kotlin.Function3  	compareTo 
kotlin.Int  div 
kotlin.Int  dp 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  timesAssign 
kotlin.Int  to 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  forEach 
kotlin.String  
isNotEmpty 
kotlin.String  orEmpty 
kotlin.String  plus 
kotlin.String  
plusAssign 
kotlin.String  to 
kotlin.String  
trimIndent 
kotlin.String  message kotlin.Throwable  
Collection kotlin.collections  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  	associate kotlin.collections  average kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  drop kotlin.collections  	emptyList kotlin.collections  first kotlin.collections  firstOrNull kotlin.collections  flatten kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  	getOrNull kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  joinToString kotlin.collections  linkedMapOf kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  max kotlin.collections  maxOf kotlin.collections  min kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  orEmpty kotlin.collections  
plusAssign kotlin.collections  toTypedArray kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  	associate kotlin.collections.List  average kotlin.collections.List  drop kotlin.collections.List  first kotlin.collections.List  firstOrNull kotlin.collections.List  flatten kotlin.collections.List  forEachIndexed kotlin.collections.List  get kotlin.collections.List  	getOrNull kotlin.collections.List  indices kotlin.collections.List  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  map kotlin.collections.List  
mapNotNull kotlin.collections.List  size kotlin.collections.List  Entry kotlin.collections.Map  entries kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  get kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  removeAt kotlin.collections.MutableList  size kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  forEachIndexed kotlin.collections.MutableSet  joinToString kotlin.collections.Set  maxOf kotlin.comparisons  minOf kotlin.comparisons  SuspendFunction1 kotlin.coroutines  SuspendFunction2 kotlin.coroutines  use 	kotlin.io  java 
kotlin.jvm  abs kotlin.math  max kotlin.math  min kotlin.math  sqrt kotlin.math  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  coerceAtMost 
kotlin.ranges  first 
kotlin.ranges  firstOrNull 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  Sequence kotlin.sequences  	associate kotlin.sequences  average kotlin.sequences  drop kotlin.sequences  first kotlin.sequences  firstOrNull kotlin.sequences  flatten kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  joinToString kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  max kotlin.sequences  maxOf kotlin.sequences  min kotlin.sequences  minOf kotlin.sequences  orEmpty kotlin.sequences  	associate kotlin.text  drop kotlin.text  first kotlin.text  firstOrNull kotlin.text  forEach kotlin.text  forEachIndexed kotlin.text  	getOrNull kotlin.text  indices kotlin.text  
isNotEmpty kotlin.text  map kotlin.text  
mapNotNull kotlin.text  max kotlin.text  maxOf kotlin.text  min kotlin.text  minOf kotlin.text  orEmpty kotlin.text  
trimIndent kotlin.text  Duration kotlin.time  seconds kotlin.time.Duration.Companion  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  
ImageUtils !kotlinx.coroutines.CoroutineScope  
InputImage !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  TwelvePalacesData !kotlinx.coroutines.CoroutineScope  	associate !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  getBitmapFromUri !kotlinx.coroutines.CoroutineScope  
isNotEmpty !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  
plusAssign !kotlinx.coroutines.CoroutineScope  to !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  catch kotlinx.coroutines.flow  emitAll kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  map kotlinx.coroutines.flow  catch kotlinx.coroutines.flow.Flow  collect kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  <SAM-CONSTRUCTOR> %kotlinx.coroutines.flow.FlowCollector  Log %kotlinx.coroutines.flow.FlowCollector  emit %kotlinx.coroutines.flow.FlowCollector  emitAll %kotlinx.coroutines.flow.FlowCollector  firstOrNull %kotlinx.coroutines.flow.FlowCollector  flow %kotlinx.coroutines.flow.FlowCollector  forEach %kotlinx.coroutines.flow.FlowCollector  getDestinyNonStreaming %kotlinx.coroutines.flow.FlowCollector  kotlinx %kotlinx.coroutines.flow.FlowCollector  openAI %kotlinx.coroutines.flow.FlowCollector  orEmpty %kotlinx.coroutines.flow.FlowCollector  Mutex kotlinx.coroutines.sync  withLock kotlinx.coroutines.sync  withLock kotlinx.coroutines.sync.Mutex  Json kotlinx.serialization.json  JsonBuilder kotlinx.serialization.json  coerceInputValues &kotlinx.serialization.json.JsonBuilder  ignoreUnknownKeys &kotlinx.serialization.json.JsonBuilder  	isLenient &kotlinx.serialization.json.JsonBuilder  	Alignment androidx.compose.animation.core  AnalysisResultCard androidx.compose.animation.core  Arrangement androidx.compose.animation.core  Bitmap androidx.compose.animation.core  Boolean androidx.compose.animation.core  Box androidx.compose.animation.core  Brush androidx.compose.animation.core  ButtonDefaults androidx.compose.animation.core  Card androidx.compose.animation.core  CardDefaults androidx.compose.animation.core  CircularProgressIndicator androidx.compose.animation.core  Color androidx.compose.animation.core  Column androidx.compose.animation.core  
Composable androidx.compose.animation.core  ContentScale androidx.compose.animation.core  DestinyResultCard androidx.compose.animation.core  	EaseInOut androidx.compose.animation.core  Easing androidx.compose.animation.core  	Exception androidx.compose.animation.core  FaceMesh androidx.compose.animation.core  FaceOverlay androidx.compose.animation.core  Float androidx.compose.animation.core  
FontWeight androidx.compose.animation.core  Icon androidx.compose.animation.core  
IconButton androidx.compose.animation.core  Icons androidx.compose.animation.core  Image androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  InfiniteTransition androidx.compose.animation.core  Int androidx.compose.animation.core  IntSize androidx.compose.animation.core  LayerControlPanel androidx.compose.animation.core  
LazyColumn androidx.compose.animation.core  List androidx.compose.animation.core  LocalConfiguration androidx.compose.animation.core  
MaterialTheme androidx.compose.animation.core  ModernDestinyButton androidx.compose.animation.core  Modifier androidx.compose.animation.core  
PaddingValues androidx.compose.animation.core  PalaceAnalysisResult androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  RoundedCornerShape androidx.compose.animation.core  Row androidx.compose.animation.core  ScaleAndOffsetResult androidx.compose.animation.core  Spacer androidx.compose.animation.core  String androidx.compose.animation.core  Text androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  TwelvePalacesData androidx.compose.animation.core  Unit androidx.compose.animation.core  Uri androidx.compose.animation.core  align androidx.compose.animation.core  androidx androidx.compose.animation.core  animateFloat androidx.compose.animation.core  
asImageBitmap androidx.compose.animation.core  	associate androidx.compose.animation.core  
background androidx.compose.animation.core  buttonColors androidx.compose.animation.core  calculateScaleAndOffset androidx.compose.animation.core  
cardColors androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  fillMaxWidth androidx.compose.animation.core  getValue androidx.compose.animation.core  height androidx.compose.animation.core  heightIn androidx.compose.animation.core  horizontalGradient androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  isEmpty androidx.compose.animation.core  
isNotEmpty androidx.compose.animation.core  launch androidx.compose.animation.core  listOf androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  onGloballyPositioned androidx.compose.animation.core  padding androidx.compose.animation.core  
plusAssign androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  remember androidx.compose.animation.core  rememberCoroutineScope androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  setValue androidx.compose.animation.core  size androidx.compose.animation.core  spacedBy androidx.compose.animation.core  to androidx.compose.animation.core  tween androidx.compose.animation.core  verticalGradient androidx.compose.animation.core  weight androidx.compose.animation.core  width androidx.compose.animation.core  animateFloat 2androidx.compose.animation.core.InfiniteTransition  Reverse *androidx.compose.animation.core.RepeatMode  ButtonDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  DestinyResultCard "androidx.compose.foundation.layout  	EaseInOut "androidx.compose.foundation.layout  ModernDestinyButton "androidx.compose.foundation.layout  
RepeatMode "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  animateFloat "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  horizontalGradient "androidx.compose.foundation.layout  infiniteRepeatable "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  rememberInfiniteTransition "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  AutoAwesome +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  DestinyResultCard +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  ModernDestinyButton +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  AutoAwesome .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  DestinyResultCard .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  ModernDestinyButton .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  isEmpty .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Arrangement +androidx.compose.foundation.layout.RowScope  AutoAwesome +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  horizontalGradient +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  LazyListLayoutInfo  androidx.compose.foundation.lazy  
LazyListState  androidx.compose.foundation.lazy  rememberLazyListState  androidx.compose.foundation.lazy  DestinyResultCard .androidx.compose.foundation.lazy.LazyItemScope  ModernDestinyButton .androidx.compose.foundation.lazy.LazyItemScope  
isNotEmpty .androidx.compose.foundation.lazy.LazyItemScope  totalItemsCount 3androidx.compose.foundation.lazy.LazyListLayoutInfo  DestinyResultCard .androidx.compose.foundation.lazy.LazyListScope  ModernDestinyButton .androidx.compose.foundation.lazy.LazyListScope  animateScrollToItem .androidx.compose.foundation.lazy.LazyListState  
layoutInfo .androidx.compose.foundation.lazy.LazyListState  AutoAwesome ,androidx.compose.material.icons.Icons.Filled  AutoAwesome &androidx.compose.material.icons.filled  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  	bodyLarge %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  ButtonDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  DestinyResultCard androidx.compose.runtime  	EaseInOut androidx.compose.runtime  ModernDestinyButton androidx.compose.runtime  
PaddingValues androidx.compose.runtime  
RepeatMode androidx.compose.runtime  Row androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  animateFloat androidx.compose.runtime  buttonColors androidx.compose.runtime  horizontalGradient androidx.compose.runtime  infiniteRepeatable androidx.compose.runtime  isEmpty androidx.compose.runtime  rememberInfiniteTransition androidx.compose.runtime  size androidx.compose.runtime  tween androidx.compose.runtime  width androidx.compose.runtime  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  width androidx.compose.ui.Modifier  width &androidx.compose.ui.Modifier.Companion  horizontalGradient "androidx.compose.ui.graphics.Brush  horizontalGradient ,androidx.compose.ui.graphics.Brush.Companion  SemiBold (androidx.compose.ui.text.font.FontWeight  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  ButtonDefaults com.wendy.face.ui.screens  CircularProgressIndicator com.wendy.face.ui.screens  DestinyResultCard com.wendy.face.ui.screens  	EaseInOut com.wendy.face.ui.screens  	Exception com.wendy.face.ui.screens  ModernDestinyButton com.wendy.face.ui.screens  
PaddingValues com.wendy.face.ui.screens  
RepeatMode com.wendy.face.ui.screens  Row com.wendy.face.ui.screens  String com.wendy.face.ui.screens  animateFloat com.wendy.face.ui.screens  buttonColors com.wendy.face.ui.screens  horizontalGradient com.wendy.face.ui.screens  infiniteRepeatable com.wendy.face.ui.screens  isEmpty com.wendy.face.ui.screens  rememberInfiniteTransition com.wendy.face.ui.screens  size com.wendy.face.ui.screens  tween com.wendy.face.ui.screens  width com.wendy.face.ui.screens  isEmpty kotlin.CharSequence  isEmpty 
kotlin.String  isEmpty kotlin.collections  isEmpty kotlin.text  times kotlin.time.Duration  LaunchedEffect androidx.compose.animation.core  verticalScroll androidx.compose.animation.core  ScrollState androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  animateScrollTo 'androidx.compose.foundation.ScrollState  maxValue 'androidx.compose.foundation.ScrollState  detectTapGestures $androidx.compose.foundation.gestures  verticalScroll "androidx.compose.foundation.layout  Int +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  Int .androidx.compose.foundation.layout.ColumnScope  verticalScroll .androidx.compose.foundation.layout.ColumnScope  Int .androidx.compose.foundation.lazy.LazyItemScope  Int .androidx.compose.foundation.lazy.LazyListScope  verticalScroll androidx.compose.runtime  pointerInput androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  detectTapGestures 3androidx.compose.ui.input.pointer.PointerInputScope  LaunchedEffect com.wendy.face.ui.screens  verticalScroll com.wendy.face.ui.screens  Int kotlin  	Companion 
kotlin.Int  	MAX_VALUE 
kotlin.Int  	MAX_VALUE kotlin.Int.Companion  Int !kotlinx.coroutines.CoroutineScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      