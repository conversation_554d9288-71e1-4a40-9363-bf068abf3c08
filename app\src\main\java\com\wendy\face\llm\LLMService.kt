package com.wendy.face.llm

import android.util.Log
import com.aallam.openai.api.chat.ChatCompletion
import com.aallam.openai.api.chat.ChatCompletionChunk
import com.aallam.openai.api.chat.ChatCompletionRequest
import com.aallam.openai.api.chat.ChatMessage
import com.aallam.openai.api.chat.ChatRole
import com.aallam.openai.api.http.Timeout
import com.aallam.openai.api.model.ModelId
import com.aallam.openai.client.OpenAI
import com.aallam.openai.client.OpenAIHost
import com.wendy.face.model.TwelvePalacesData
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.json.Json
import kotlin.time.Duration.Companion.seconds

class LLMService {

    private val openAI: OpenAI

    init {
        openAI = OpenAI(
            token = com.wendy.face.BuildConfig.VOLC_API_KEY,
            timeout = Timeout(socket = 60.seconds),
            host = OpenAIHost(baseUrl = "https://ark.cn-beijing.volces.com/api/v3/"),
            httpClientConfig = {
                install(ContentNegotiation) {
                    json(Json {
                        ignoreUnknownKeys = true
                        coerceInputValues = true
                        isLenient = true
                    })
                }
            }
        )
    }

    fun getDestiny(palaces: TwelvePalacesData): Flow<String> {
        val prompt = """
            请根据以下十二宫的量化分析结果，推断命格：
            ${palaces.palaces.entries.joinToString("\n") { "${it.key}: ${it.value}" }}
        """.trimIndent()

        val chatCompletionRequest = ChatCompletionRequest(
            model = ModelId("deepseek-v3-250324"),
            messages = listOf(
                ChatMessage(
                    role = ChatRole.System,
                    content = "你是一位精通命理学的专家。"
                ),
                ChatMessage(
                    role = ChatRole.User,
                    content = prompt
                )
            )
        )

        return try {
            // Try streaming first
            val completions: Flow<ChatCompletionChunk> = openAI.chatCompletions(chatCompletionRequest)

            completions
                .map { chunk ->
                    val content = chunk.choices.firstOrNull()?.delta?.content.orEmpty()
                    android.util.Log.d("LLMService", "Received chunk: $content")
                    content
                }
                .catch { exception ->
                    Log.e("LLMService", "Error in streaming response, falling back to non-streaming", exception)
                    // Fallback to non-streaming
                    emitAll(flow { emitAll(getDestinyNonStreaming(chatCompletionRequest)) })
                }
        } catch (exception: Exception) {
            Log.e("LLMService", "Error creating streaming request, using non-streaming", exception)
            flow { emitAll(getDestinyNonStreaming(chatCompletionRequest)) }
        }
    }

    private suspend fun getDestinyNonStreaming(request: ChatCompletionRequest): Flow<String> = flow {
        try {
            val completion: ChatCompletion = openAI.chatCompletion(request)
            val content = completion.choices.firstOrNull()?.message?.content.orEmpty()
            Log.d("LLMService", "Received non-streaming response: $content")

            // Emit the content character by character to simulate streaming
            content.forEach { char ->
                emit(char.toString())
                kotlinx.coroutines.delay(10) // Small delay to simulate streaming
            }
        } catch (exception: Exception) {
            Log.e("LLMService", "Error in non-streaming response", exception)
            emit("抱歉，无法获取命理分析结果。请稍后重试。")
        }
    }
}