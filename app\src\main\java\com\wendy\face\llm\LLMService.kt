package com.wendy.face.llm

import android.util.Log
import com.aallam.openai.api.chat.ChatCompletionChunk
import com.aallam.openai.api.chat.ChatCompletionRequest
import com.aallam.openai.api.chat.ChatMessage
import com.aallam.openai.api.chat.ChatRole
import com.aallam.openai.api.http.Timeout
import com.aallam.openai.api.model.ModelId
import com.aallam.openai.client.OpenAI
import com.aallam.openai.client.OpenAIHost
import com.wendy.face.model.TwelvePalacesData
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.json.Json
import kotlin.time.Duration.Companion.seconds

class LLMService {

    private val openAI: OpenAI

    init {
        openAI = OpenAI(
            token = com.wendy.face.BuildConfig.VOLC_API_KEY,
            timeout = Timeout(socket = 60.seconds),
            host = OpenAIHost(baseUrl = "https://ark.cn-beijing.volces.com/api/v3/"),
            httpClientConfig = {
                install(ContentNegotiation) {
                    json(Json {
                        ignoreUnknownKeys = true
                        coerceInputValues = true
                        isLenient = true
                    })
                }
            }
        )
    }

    fun getDestiny(palaces: TwelvePalacesData): Flow<String> {
        val prompt = """
            请根据以下十二宫的量化分析结果，推断命格：
            ${palaces.palaces.entries.joinToString("\n") { "${it.key}: ${it.value}" }}
        """.trimIndent()

        val chatCompletionRequest = ChatCompletionRequest(
            model = ModelId("deepseek-v3-250324"),
            messages = listOf(
                ChatMessage(
                    role = ChatRole.System,
                    content = "你是一位精通命理学的专家。"
                ),
                ChatMessage(
                    role = ChatRole.User,
                    content = prompt
                )
            )
        )

        val completions: Flow<ChatCompletionChunk> = openAI.chatCompletions(chatCompletionRequest)

        return completions.map { chunk ->
            val content = chunk.choices.first().delta?.content.orEmpty()
            android.util.Log.d("LLMService", "Received chunk: $content")
            content
        }
    }
}