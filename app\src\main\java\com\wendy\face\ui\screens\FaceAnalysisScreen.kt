package com.wendy.face.ui.screens

import android.graphics.Bitmap
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.analyzer.PalaceAnalysisResult
import com.wendy.face.llm.LLMService
import com.wendy.face.model.TwelvePalacesData
import com.wendy.face.ui.components.FaceOverlay
import com.wendy.face.ui.components.LayerControlPanel
import kotlinx.coroutines.launch

/**
 * 人脸分析结果展示界面
 * @param capturedBitmap 拍摄的照片
 * @param faceMeshes 检测到的人脸网格
 * @param analysisResults 分析结果
 * @param isBackCamera 是否使用后置摄像头
 * @param onBack 返回按钮的回调
 * @param onReanalyze 重新分析按钮的回调
 */
@Composable
fun FaceAnalysisScreen(
    capturedImageUri: Uri,
    capturedBitmap: Bitmap, // 仍需要bitmap用于获取尺寸信息
    faceMeshes: List<FaceMesh>,
    analysisResults: List<PalaceAnalysisResult>,
    isBackCamera: Boolean,
    onBack: () -> Unit,
    onReanalyze: (() -> Unit)? = null
) {
    var imageDisplaySize by remember { mutableStateOf(IntSize.Zero) }
    var imageDisplayOffset by remember { mutableStateOf(androidx.compose.ui.geometry.Offset.Zero) }
    val coroutineScope = rememberCoroutineScope()
    val llmService = remember { LLMService() }
    var destinyText by remember { mutableStateOf("") }

    // 图层控制状态
    var showPhoto by remember { mutableStateOf(true) }
    var showAnalysisReport by remember { mutableStateOf(true) }
    var showKeypoints by remember { mutableStateOf(true) }
    var showFacialFeatures by remember { mutableStateOf(true) }

    Box(modifier = Modifier.fillMaxSize()) {
        // 1. 将拍摄的照片作为背景（根据图层控制显示/隐藏）
        if (showPhoto) {
            Image(
                bitmap = capturedBitmap.asImageBitmap(),
                contentDescription = "Captured Photo",
                modifier = Modifier
                    .fillMaxSize()
                    .onGloballyPositioned { layoutCoordinates ->
                        // 获取Image Composable的实际尺寸和位置
                        imageDisplaySize = layoutCoordinates.size
                        imageDisplayOffset = layoutCoordinates.localToRoot(androidx.compose.ui.geometry.Offset.Zero)
                    },
                contentScale = ContentScale.Crop
            )
        } else {
            // 当照片隐藏时，仍需要获取尺寸信息用于关键点定位
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black)
                    .onGloballyPositioned { layoutCoordinates ->
                        imageDisplaySize = layoutCoordinates.size
                        imageDisplayOffset = layoutCoordinates.localToRoot(androidx.compose.ui.geometry.Offset.Zero)
                    }
            )
        }

        // 2. 在照片上叠加人脸关键点
        if (imageDisplaySize != IntSize.Zero && faceMeshes.isNotEmpty()) {
            val scaleAndOffset = calculateScaleAndOffset(
                imageWidth = capturedBitmap.width,
                imageHeight = capturedBitmap.height,
                viewWidth = imageDisplaySize.width.toFloat(),
                viewHeight = imageDisplaySize.height.toFloat()
            )

            FaceOverlay(
                faceMeshes = faceMeshes,
                scaleX = scaleAndOffset.scaleX,
                scaleY = scaleAndOffset.scaleY,
                offsetX = scaleAndOffset.offsetX,
                offsetY = scaleAndOffset.offsetY,
                isBackCamera = isBackCamera,
                isPreviewMode = false,
                show3DPoints = false,
                showKeypoints = showKeypoints,
                showFaceContour = showKeypoints, // 绿色关键点和面部轮廓一起控制
                showFacialFeatures = showFacialFeatures,
                showPalaceMarkers = true // 宫位标记始终显示
            )
        }

        // 添加一个从下到上的渐变蒙层，让文字更清晰
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.verticalGradient(
                        colors = listOf(Color.Transparent, Color.Black.copy(alpha = 0.8f)),
                        startY = 600f // 从屏幕大约1/3处开始渐变
                    )
                )
        )

        // 顶部返回按钮
        Box(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(16.dp)
        ) {
            IconButton(onClick = onBack) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "返回相机",
                    tint = Color.White
                )
            }
        }

        // 右侧图层控制面板（从上边30%位置开始）
        LayerControlPanel(
            showPhoto = showPhoto,
            showAnalysisReport = showAnalysisReport,
            showKeypoints = showKeypoints,
            showFacialFeatures = showFacialFeatures,
            onPhotoToggle = { showPhoto = it },
            onAnalysisReportToggle = { showAnalysisReport = it },
            onKeypointsToggle = { showKeypoints = it },
            onFacialFeaturesToggle = { showFacialFeatures = it },
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = LocalConfiguration.current.screenHeightDp.dp * 0.3f, end = 8.dp)
        )

        // 底部内容区域
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
        ) {

            Spacer(modifier = Modifier.weight(1f))

            // 显示检测状态信息
            if (faceMeshes.isEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.8f))
                ) {
                    Text(
                        text = "未检测到人脸\n请确保照片中有清晰的人脸，然后点击\"重新分析\"",
                        color = Color.White,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            } else if (analysisResults.isEmpty()) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(containerColor = Color(0xFFFF9800).copy(alpha = 0.8f))
                ) {
                    Text(
                        text = "检测到 ${faceMeshes.size} 张人脸，但分析结果为空\n点击\"重新分析\"重试",
                        color = Color.White,
                        modifier = Modifier.padding(16.dp),
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            }

            // 3. 底部展示分析结果的滚动列表（根据图层控制显示/隐藏）
            if (analysisResults.isNotEmpty() && showAnalysisReport) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(max = 400.dp) // 限制最大高度
                        .padding(horizontal = 8.dp)
                        .background(
                            Color.Black.copy(alpha = 0.6f),
                            RoundedCornerShape(16.dp)
                        )
                        .padding(12.dp)
                ) {
                    LazyColumn(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(analysisResults) { result ->
                            AnalysisResultCard(result = result)
                        }

                        item {
                            Button(onClick = {
                                coroutineScope.launch {
                                    val palaces = TwelvePalacesData(analysisResults.associate { it.palaceName to it.description })
                                    llmService.getDestiny(palaces).collect { chunk ->
                                        destinyText += chunk
                                    }
                                }
                            }) {
                                Text("推断命格")
                            }
                        }

                        if (destinyText.isNotEmpty()) {
                            item {
                                Text(text = destinyText, color = Color.White)
                            }
                        }
                    }
                }
            }
        }
    }
}

private data class ScaleAndOffsetResult(
    val scaleX: Float,
    val scaleY: Float,
    val offsetX: Float,
    val offsetY: Float
)

/**
 * 计算在ContentScale.Crop模式下的缩放比例和偏移量
 */
private fun calculateScaleAndOffset(
    imageWidth: Int,
    imageHeight: Int,
    viewWidth: Float,
    viewHeight: Float
): ScaleAndOffsetResult {
    val imageAspectRatio = imageWidth.toFloat() / imageHeight.toFloat()
    val viewAspectRatio = viewWidth / viewHeight

    val scale: Float
    val offsetX: Float
    val offsetY: Float

    if (imageAspectRatio > viewAspectRatio) {
        // 图片比视图更宽，高度填满视图，宽度裁剪
        scale = viewHeight / imageHeight.toFloat()
        val scaledImageWidth = imageWidth * scale
        offsetX = (viewWidth - scaledImageWidth) / 2f
        offsetY = 0f
    } else {
        // 图片比视图更高，宽度填满视图，高度裁剪
        scale = viewWidth / imageWidth.toFloat()
        val scaledImageHeight = imageHeight * scale
        offsetX = 0f
        offsetY = (viewHeight - scaledImageHeight) / 2f
    }

    return ScaleAndOffsetResult(scale, scale, offsetX, offsetY)
}


/**
 * 单个宫位分析结果的卡片UI
 */
@Composable
private fun AnalysisResultCard(result: PalaceAnalysisResult) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Text(
            text = result.palaceName,
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            color = Color.White
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = result.description,
            style = MaterialTheme.typography.bodyMedium,
            color = Color.White.copy(alpha = 0.9f),
            lineHeight = 20.sp
        )
    }
}