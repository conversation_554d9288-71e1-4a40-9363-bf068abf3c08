package com.wendy.face.ui.screens

import android.content.Context
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(onBack: () -> Unit) {
    val context = LocalContext.current
    val sharedPreferences = remember {
        context.getSharedPreferences("face_app_settings", Context.MODE_PRIVATE)
    }

    var age by remember { mutableStateOf(sharedPreferences.getString("age", "") ?: "") }
    var part by remember { mutableStateOf(sharedPreferences.getString("part", "") ?: "") }
    var request by remember { mutableStateOf(sharedPreferences.getString("request", "") ?: "") }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("设置") },
                navigationIcon = {
                    IconButton(onClick = onBack) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .padding(paddingValues)
                .padding(16.dp)
                .fillMaxSize(),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            OutlinedTextField(
                value = age,
                onValueChange = {
                    age = it
                    with(sharedPreferences.edit()) {
                        putString("age", it)
                        apply()
                    }
                },
                label = { Text("年龄") },
                modifier = Modifier.fillMaxWidth()
            )

            OutlinedTextField(
                value = part,
                onValueChange = {
                    part = it
                    with(sharedPreferences.edit()) {
                        putString("part", it)
                        apply()
                    }
                },
                label = { Text("部位") },
                modifier = Modifier.fillMaxWidth()
            )

            OutlinedTextField(
                value = request,
                onValueChange = {
                    request = it
                    with(sharedPreferences.edit()) {
                        putString("request", it)
                        apply()
                    }
                },
                label = { Text("诉求") },
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}